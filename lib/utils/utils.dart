import 'package:intl/intl.dart';
import 'package:logger/logger.dart';

class Utils {
  static const sourceClass = "Utils";
  static bool isValidExpireDateFormat(final String dateString) {
    if (dateString.trim() == "" || dateString.trim().length != 8) {
      return false;
    } else {
      try {
        int day = int.parse(dateString.substring(0, 2));
        int month = int.parse(dateString.substring(2, 4));
        int year = int.parse(dateString.substring(4, 8));

        if (month < 1 || month > 12) {
          return false;
        }
        int maxDay = 31;
        switch (month) {
          case 2:
            maxDay = 28;
            if (isLeapYear(year)) {
              maxDay = 29;
            }
            break;
          case 4:
          case 6:
          case 9:
          case 11:
            maxDay = 30;
            break;
          default:
            maxDay = 31;
            break;
        }

        if (day < 1 || day > maxDay) {
          return false;
        }
      } catch (e) {
        Logger.logError(sourceClass, "isValidExpireDateFormat", e.toString());
        return false;
      }
      return true;
    }
  }

  static bool isLeapYear(int year) {
    if (year % 4 == 0) {
      if (year % 100 == 0 && year % 400 != 0) {
        return false;
      }
      return true;
    }
    return false;
  }

  static String getCurrentDate() {
    DateTime now = DateTime.now();
    var dateFormat = DateFormat("MM/dd/yy");
    String date = dateFormat.format(now);
    return date;
  }
}

//	Generated using Unvired Modeller - Build R-4.000.0139


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "BREWERY".
*/	
class BREWERY_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "BREWERY_HEADER";
	
	// Brewery ID
	static const String FIELD_BREWERY_ID = "BREWERY_ID";

	// Brewery Description
	static const String FIELD_BREWERY_DESC = "BREWERY_DESC";
	
    String? _brewery_id;
    String? _brewery_desc;
	
	BREWERY_HEADER({
			required brewery_id,
			brewery_desc}) :
			_brewery_id = brewery_id,
			_brewery_desc = brewery_desc {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	BREWERY_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		
		_brewery_id = json[FIELD_BREWERY_ID]; 
		_brewery_desc = json[FIELD_BREWERY_DESC]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
    
		data[FIELD_BREWERY_ID] = _brewery_id;
		data[FIELD_BREWERY_DESC] = _brewery_desc;

		return data;
  }
  
	String? get brewery_id => this._brewery_id;
	
	set brewery_id(String? brewery_id) => this._brewery_id = brewery_id;	

	String? get brewery_desc => this._brewery_desc;
	
	set brewery_desc(String? brewery_desc) => this._brewery_desc = brewery_desc;	
	
}
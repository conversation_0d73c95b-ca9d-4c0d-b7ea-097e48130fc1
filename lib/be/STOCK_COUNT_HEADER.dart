//	Generated using Unvired Modeller - Build R-4.000.0139


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "STOCK_COUNT".
*/	
class STOCK_COUNT_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "STOCK_COUNT_HEADER";
	
	// Brewery ID
	static const String FIELD_BREWERY_ID = "BREWERY_ID";

	// Brewery Description
	static const String FIELD_DESC = "DESC";
	
    String? _brewery_id;
    String? _desc;
	
	STOCK_COUNT_HEADER({
			required brewery_id,
			desc}) :
			_brewery_id = brewery_id,
			_desc = desc {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	STOCK_COUNT_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		
		_brewery_id = json[FIELD_BREWERY_ID]; 
		_desc = json[FIELD_DESC]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
    
		data[FIELD_BREWERY_ID] = _brewery_id;
		data[FIELD_DESC] = _desc;

		return data;
  }
  
	String? get brewery_id => this._brewery_id;
	
	set brewery_id(String? brewery_id) => this._brewery_id = brewery_id;	

	String? get desc => this._desc;
	
	set desc(String? desc) => this._desc = desc;	
	
}
//	Generated using Unvired Modeller - Build R-4.000.0139


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "MATERIAL".
*/	
class MATERIAL_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "MATERIAL_HEADER";
	
	// Material Number
	static const String FIELD_MAT_NO = "MAT_NO";

	// Material Description (Short Text)
	static const String FIELD_DESCRIPTION = "DESCRIPTION";

	// Batch management requirement indicator
	static const String FIELD_BATCH_REQ = "BATCH_REQ";

	// Barcode used for Raw Material Count
	static const String FIELD_BARCODE = "BARCODE";

	// Convert to Major UOM
	static const String FIELD_MAJOR_UOM = "MAJOR_UOM";

	// Convert to Minor UOM
	static const String FIELD_MINOR_UOM = "MINOR_UOM";

	// General Flag
	static const String FIELD_EXPIRY_REQ = "EXPIRY_REQ";
	
    String? _mat_no;
    String? _description;
    String? _batch_req;
    String? _barcode;
    String? _major_uom;
    String? _minor_uom;
    String? _expiry_req;
	
	MATERIAL_HEADER({
			required mat_no,
			description,
			batch_req,
			barcode,
			major_uom,
			minor_uom,
			expiry_req}) :
			_mat_no = mat_no,
			_description = description,
			_batch_req = batch_req,
			_barcode = barcode,
			_major_uom = major_uom,
			_minor_uom = minor_uom,
			_expiry_req = expiry_req {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	MATERIAL_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		
		_mat_no = json[FIELD_MAT_NO]; 
		_description = json[FIELD_DESCRIPTION]; 
		_batch_req = json[FIELD_BATCH_REQ]; 
		_barcode = json[FIELD_BARCODE]; 
		_major_uom = json[FIELD_MAJOR_UOM]; 
		_minor_uom = json[FIELD_MINOR_UOM]; 
		_expiry_req = json[FIELD_EXPIRY_REQ]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
    
		data[FIELD_MAT_NO] = _mat_no;
		data[FIELD_DESCRIPTION] = _description;
		data[FIELD_BATCH_REQ] = _batch_req;
		data[FIELD_BARCODE] = _barcode;
		data[FIELD_MAJOR_UOM] = _major_uom;
		data[FIELD_MINOR_UOM] = _minor_uom;
		data[FIELD_EXPIRY_REQ] = _expiry_req;

		return data;
  }
  
	String? get mat_no => this._mat_no;
	
	set mat_no(String? mat_no) => this._mat_no = mat_no;	

	String? get description => this._description;
	
	set description(String? description) => this._description = description;	

	String? get batch_req => this._batch_req;
	
	set batch_req(String? batch_req) => this._batch_req = batch_req;	

	String? get barcode => this._barcode;
	
	set barcode(String? barcode) => this._barcode = barcode;	

	String? get major_uom => this._major_uom;
	
	set major_uom(String? major_uom) => this._major_uom = major_uom;	

	String? get minor_uom => this._minor_uom;
	
	set minor_uom(String? minor_uom) => this._minor_uom = minor_uom;	

	String? get expiry_req => this._expiry_req;
	
	set expiry_req(String? expiry_req) => this._expiry_req = expiry_req;	
	
}
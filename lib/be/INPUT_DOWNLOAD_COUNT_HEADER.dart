//	Generated using Unvired Modeller - Build R-4.000.0142


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "INPUT_DOWNLOAD_COUNT".
*/	
class INPUT_DOWNLOAD_COUNT_HEADER extends DataStructure {
	
	static const String TABLE_NAME = "INPUT_DOWNLOAD_COUNT_HEADER";
	
	// Brewery ID char type
	static const String FIELD_BREWERY_ID = "BREWERY_ID";

	// Brewery Area char type
	static const String FIELD_AREA = "AREA";
	
    String? _brewery_id;
    String? _area;
	
	INPUT_DOWNLOAD_COUNT_HEADER({
			required brewery_id,
			required area}) :
			_brewery_id = brewery_id,
			_area = area {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	INPUT_DOWNLOAD_COUNT_HEADER.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	
    	lid = json[FieldLid];    	
    	hasConflicts = json[FieldConflict];
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		infoMsgCat = json[FieldInfoMsgCat];
		
		_brewery_id = json[FIELD_BREWERY_ID]; 
		_area = json[FIELD_AREA]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		
		data[FieldLid] = lid;
		data[FieldConflict] = hasConflicts;
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
		data[FieldInfoMsgCat] = infoMsgCat;
    
		data[FIELD_BREWERY_ID] = _brewery_id;
		data[FIELD_AREA] = _area;

		return data;
  }
  
	String? get brewery_id => this._brewery_id;
	
	set brewery_id(String? brewery_id) => this._brewery_id = brewery_id;	

	String? get area => this._area;
	
	set area(String? area) => this._area = area;	
	
}
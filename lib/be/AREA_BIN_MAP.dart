//	Generated using Unvired Modeller - Build R-4.000.0139


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "BREWERY".
*/	
class AREA_BIN_MAP extends DataStructure {
	
	static const String TABLE_NAME = "AREA_BIN_MAP";
	
	// Brewery Area
	static const String FIELD_BREWERYAREA = "BREWERYAREA";

	// Area Section
	static const String FIELD_AREASECTION = "AREASECTION";

	// Bin
	static const String FIELD_BIN = "BIN";
	
    String? _breweryarea;
    String? _areasection;
    String? _bin;
	
	AREA_BIN_MAP({
			required breweryarea,
			required areasection,
			required bin}) :
			_breweryarea = breweryarea,
			_areasection = areasection,
			_bin = bin {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	AREA_BIN_MAP.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		
		_breweryarea = json[FIELD_BREWERYAREA]; 
		_areasection = json[FIELD_AREASECTION]; 
		_bin = json[FIELD_BIN]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
    
		data[FIELD_BREWERYAREA] = _breweryarea;
		data[FIELD_AREASECTION] = _areasection;
		data[FIELD_BIN] = _bin;

		return data;
  }
  
	String? get breweryarea => this._breweryarea;
	
	set breweryarea(String? breweryarea) => this._breweryarea = breweryarea;	

	String? get areasection => this._areasection;
	
	set areasection(String? areasection) => this._areasection = areasection;	

	String? get bin => this._bin;
	
	set bin(String? bin) => this._bin = bin;	
	
}
//	Generated using Unvired Modeller - Build R-4.000.0139


import 'package:unvired_sdk/unvired_sdk.dart';

/*
This class is part of the BE "STOCK_COUNT".
*/	
class STOCK_COUNT_ITEM extends DataStructure {
	
	static const String TABLE_NAME = "STOCK_COUNT_ITEM";
	
	// Material Number
	static const String FIELD_MAT_NO = "MAT_NO";

	// Batch Number
	static const String FIELD_BATCH_NO = "BATCH_NO";

	// DOM or Best Before Date
	static const String FIELD_DOMBBD = "DOMBBD";

	// Brewery Area
	static const String FIELD_AREA = "AREA";

	// Area Section
	static const String FIELD_AREASECTION = "AREASECTION";

	// Bin
	static const String FIELD_BIN = "BIN";

	// Quantity in Major UOM
	static const String FIELD_MAJOR_UNIT_QTY = "MAJOR_UNIT_QTY";

	// Quantity in Minor UOM
	static const String FIELD_MINOR_UNIT_QTY = "MINOR_UNIT_QTY";

	// Material Number
	static const String FIELD_PALLET = "PALLET";

	// Status Indicator
	static const String FIELD_STATUS = "STATUS";

	// Count Date
	static const String FIELD_COUNT_DATE = "COUNT_DATE";

	// Count Direction
	static const String FIELD_DIRECTION = "DIRECTION";

	// Count Id
	static const String FIELD_COUNT_ID = "COUNT_ID";

	// confirm a split bin status
	static const String FIELD_SPLIT_BIN = "SPLIT_BIN";

	// Value date, format DDMMYYYY
	static const String FIELD_EXPIRY_DT = "EXPIRY_DT";
	
    String? _mat_no;
    String? _batch_no;
    String? _dombbd;
    String? _area;
    String? _areasection;
    String? _bin;
    double? _major_unit_qty;
    double? _minor_unit_qty;
    String? _pallet;
    String? _status;
    String? _count_date;
    String? _direction;
    int? _count_id;
    String? _split_bin;
    String? _expiry_dt;
	
	STOCK_COUNT_ITEM({
			mat_no,
			batch_no,
			dombbd,
			area,
			areasection,
			bin,
			major_unit_qty,
			minor_unit_qty,
			pallet,
			status,
			count_date,
			direction,
			required count_id,
			split_bin,
			expiry_dt}) :
			_mat_no = mat_no,
			_batch_no = batch_no,
			_dombbd = dombbd,
			_area = area,
			_areasection = areasection,
			_bin = bin,
			_major_unit_qty = major_unit_qty,
			_minor_unit_qty = minor_unit_qty,
			_pallet = pallet,
			_status = status,
			_count_date = count_date,
			_direction = direction,
			_count_id = count_id,
			_split_bin = split_bin,
			_expiry_dt = expiry_dt {		
		lid = FrameworkHelper.getUUID();
		tableName = TABLE_NAME;
	}
  
  
	STOCK_COUNT_ITEM.fromJson(Map<String, dynamic> json) {
		tableName = TABLE_NAME;
    	fid = json[FieldFid];
    	lid = json[FieldLid];    	
    	
		objectStatus = ObjectStatus.values[json[FieldObjectStatus]];
		syncStatus = SyncStatus.values[json[FieldSyncStatus]];
		timeStamp = json[FieldTimestamp];
		
		_mat_no = json[FIELD_MAT_NO]; 
		_batch_no = json[FIELD_BATCH_NO]; 
		_dombbd = json[FIELD_DOMBBD]; 
		_area = json[FIELD_AREA]; 
		_areasection = json[FIELD_AREASECTION]; 
		_bin = json[FIELD_BIN]; 
		_major_unit_qty = json[FIELD_MAJOR_UNIT_QTY]; 
		_minor_unit_qty = json[FIELD_MINOR_UNIT_QTY]; 
		_pallet = json[FIELD_PALLET]; 
		_status = json[FIELD_STATUS]; 
		_count_date = json[FIELD_COUNT_DATE]; 
		_direction = json[FIELD_DIRECTION]; 
		_count_id = json[FIELD_COUNT_ID]; 
		_split_bin = json[FIELD_SPLIT_BIN]; 
		_expiry_dt = json[FIELD_EXPIRY_DT]; 
  	}
	

	Map<String, dynamic> toJson() {
		final Map<String, dynamic> data = <String, dynamic>{};
		data[FieldTableName] = TABLE_NAME;
		data[FieldFid] = fid;
		data[FieldLid] = lid;
		
		data[FieldObjectStatus] =  objectStatus.index;
		data[FieldSyncStatus] = syncStatus.index;
		data[FieldTimestamp] = timeStamp;
    
		data[FIELD_MAT_NO] = _mat_no;
		data[FIELD_BATCH_NO] = _batch_no;
		data[FIELD_DOMBBD] = _dombbd;
		data[FIELD_AREA] = _area;
		data[FIELD_AREASECTION] = _areasection;
		data[FIELD_BIN] = _bin;
		data[FIELD_MAJOR_UNIT_QTY] = _major_unit_qty;
		data[FIELD_MINOR_UNIT_QTY] = _minor_unit_qty;
		data[FIELD_PALLET] = _pallet;
		data[FIELD_STATUS] = _status;
		data[FIELD_COUNT_DATE] = _count_date;
		data[FIELD_DIRECTION] = _direction;
		data[FIELD_COUNT_ID] = _count_id;
		data[FIELD_SPLIT_BIN] = _split_bin;
		data[FIELD_EXPIRY_DT] = _expiry_dt;

		return data;
  }
  
	String? get mat_no => this._mat_no;
	
	set mat_no(String? mat_no) => this._mat_no = mat_no;	

	String? get batch_no => this._batch_no;
	
	set batch_no(String? batch_no) => this._batch_no = batch_no;	

	String? get dombbd => this._dombbd;
	
	set dombbd(String? dombbd) => this._dombbd = dombbd;	

	String? get area => this._area;
	
	set area(String? area) => this._area = area;	

	String? get areasection => this._areasection;
	
	set areasection(String? areasection) => this._areasection = areasection;	

	String? get bin => this._bin;
	
	set bin(String? bin) => this._bin = bin;	

	double? get major_unit_qty => this._major_unit_qty;
	
	set major_unit_qty(double? major_unit_qty) => this._major_unit_qty = major_unit_qty;	

	double? get minor_unit_qty => this._minor_unit_qty;
	
	set minor_unit_qty(double? minor_unit_qty) => this._minor_unit_qty = minor_unit_qty;	

	String? get pallet => this._pallet;
	
	set pallet(String? pallet) => this._pallet = pallet;	

	String? get status => this._status;
	
	set status(String? status) => this._status = status;	

	String? get count_date => this._count_date;
	
	set count_date(String? count_date) => this._count_date = count_date;	

	String? get direction => this._direction;
	
	set direction(String? direction) => this._direction = direction;	

	int? get count_id => this._count_id;
	
	set count_id(int? count_id) => this._count_id = count_id;	

	String? get split_bin => this._split_bin;
	
	set split_bin(String? split_bin) => this._split_bin = split_bin;	

	String? get expiry_dt => this._expiry_dt;
	
	set expiry_dt(String? expiry_dt) => this._expiry_dt = expiry_dt;	
	
}
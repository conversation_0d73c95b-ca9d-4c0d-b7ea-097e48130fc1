import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:nsts/screens/calculator.dart';
import 'package:nsts/screens/count_page.dart';
import 'package:nsts/screens/home_page.dart';
import 'package:nsts/screens/prepare_for_count_page.dart';
import 'package:nsts/screens/splash_screen.dart';
import 'package:nsts/screens/stock_take_system_login_page.dart';

import 'login_page.dart';

class RouteGenerator {
  Route<dynamic> generateRoute(RouteSettings settings) {
    final args = settings.arguments;

    switch (settings.name) {
      case SplashScreen.routeName:
        return CupertinoPageRoute(
          builder: (context) => SplashScreen(),
        );
      case LoginPage.routeName:
        return CupertinoPageRoute(
          builder: (context) =>
              LoginPage(loginPageIntent: args as LoginPageIntent),
        );
      case StockTakeSystemLoginPage.routeName:
        return CupertinoPageRoute(
          builder: (context) => StockTakeSystemLoginPage(),
        );
      case HomePage.routeName:
        return CupertinoPageRoute(
          builder: (context) => HomePage(),
        );
      case PrepareForCountPage.routeName:
        return CupertinoPageRoute(
          builder: (context) => PrepareForCountPage(),
        );
      case CountPage.routeName:
        return CupertinoPageRoute(
          builder: (context) =>
              CountPage(countPageIntent: args as CountPageIntent),
        );
      case Calculator.routeName:
        return CupertinoPageRoute(
          builder: (context) => Calculator(requestcode: args as String),
        );

      default:
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute() {
    return CupertinoPageRoute(builder: (_) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
        ),
        body: const Center(
          child: Text('Error occur'),
        ),
      );
    });
  }
}

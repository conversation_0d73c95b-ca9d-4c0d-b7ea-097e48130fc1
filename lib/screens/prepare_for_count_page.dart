import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:nsts/helper/db_helper.dart';
import 'package:nsts/screens/count_page.dart';
import 'package:nsts/screens/home_page.dart';
import 'package:nsts/utils/app_colors.dart';
import 'package:nsts/utils/constants.dart';
import 'package:nsts/widgets/ui_textstyle_helper.dart';
import 'package:unvired_ui/ui_helper.dart';
import 'package:unvired_ui/widgets/unvired_confirmation_dialog.dart';
import 'package:unvired_ui/widgets/unvired_elevated_button.dart';

import '../be/BREWERY_HEADER.dart';
import '../helper/ui_helper.dart';

class PrepareForCountPage extends StatefulWidget {
  static const routeName = '/prepareForCountPage';
  const PrepareForCountPage({Key? key}) : super(key: key);

  @override
  State<PrepareForCountPage> createState() => _PrepareForCountPageState();
}

class _PrepareForCountPageState extends State<PrepareForCountPage> {
  String appBar = '';
  List<String> breweryAreaSpinnerValues = [];
  List<String> areaSelectionValues = [];
  String radioButtonValue = 'Forward';
  String sequence = '';
  String dropdownBreweryValue = '';
  String dropdownAreaSelectionValue = '';

  @override
  void initState() {
    _initAndSetAppBar();
    _initBreweryAreaSpinnerValues();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.pushNamed(context, HomePage.routeName);
        return true;
      },
      child: Scaffold(
        appBar: AppBar(title: Text(appBar), automaticallyImplyLeading: false, backgroundColor: AppColor.primaryDark),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [countCard(), buttonsField()],
          ),
        ),
      ),
    );
  }

  Widget countCard() {
    return Card(
      elevation: 4,
      margin: EdgeInsets.all(8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      child: Padding(
        padding: EdgeInsets.only(top: 8, left: 8, right: 8),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Padding(
            padding: const EdgeInsets.only(top: 6),
            child: Center(
                child: Text(AppLocalizations.of(context)!.startCount, style: UITextStyleHelper.textStyleMedium(context, color: AppColor.black))),
          ),
          breweryField(),
          areaSelectionField(),
          radioSelectionField(),
        ]),
      ),
    );
  }

  Widget breweryField() {
    return Column(children: [
      Padding(
        padding: const EdgeInsets.only(top: 8.0, left: 12.0),
        child: Align(
            alignment: Alignment.topLeft,
            child: Text(
              AppLocalizations.of(context)!.breweryArea,
              style: UITextStyleHelper.textStyleMedium(context, color: AppColor.grey),
            )),
      ),
      Row(
        children: [
          Expanded(
            child: Container(
              color: AppColor.white,
              child: DropdownButtonHideUnderline(
                child: ButtonTheme(
                  alignedDropdown: true,
                  child: DropdownButton(
                    value: dropdownBreweryValue,
                    items: breweryAreaSpinnerValues.map((String value) {
                      return DropdownMenuItem(
                        value: value,
                        child: Text(
                          value,
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) async {
                      setState(() {
                        dropdownBreweryValue = newValue!;
                      });
                      if (newValue == breweryAreaSpinnerValues.first) {
                        sequence = '';
                      }
                      areaSelectionValues = await _initAreaSelectionSpinnerValues(dropdownBreweryValue);
                      dropdownAreaSelectionValue = areaSelectionValues.first;
                    },
                    style: TextStyle(color: AppColor.blue, fontSize: 20),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    ]);
  }

  Widget areaSelectionField() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 12.0),
          child: Align(
              alignment: Alignment.topLeft,
              child: Text(
                AppLocalizations.of(context)!.areaSelection,
                style: UITextStyleHelper.textStyleMedium(context, color: AppColor.grey),
              )),
        ),
        Row(
          children: [
            Expanded(
              child: Container(
                color: AppColor.white,
                child: DropdownButtonHideUnderline(
                  child: ButtonTheme(
                    alignedDropdown: true,
                    child: DropdownButton(
                      value: dropdownAreaSelectionValue,
                      style: TextStyle(color: AppColor.blue, fontSize: 20),
                      items: areaSelectionValues.map((String value) {
                        return DropdownMenuItem(
                          value: value,
                          child: Text(value.split("&").first),
                        );
                      }).toList(),
                      onChanged: (String? newValue) async {
                        dropdownAreaSelectionValue = newValue!;
                        if (newValue != areaSelectionValues.first) {
                          sequence = newValue.split("&")[1];
                        } else {
                          sequence = '';
                        }
                        setState(() {});
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget radioSelectionField() {
    return Padding(
      padding: EdgeInsets.only(top: 8.0),
      child: Row(children: [
        Expanded(
          child: Row(children: [
            Radio<String>(
              value: AppLocalizations.of(context)!.forward,
              groupValue: radioButtonValue,
              onChanged: (val) {
                setState(() {
                  radioButtonValue = val!;
                });
              },
            ),
            Text(
              AppLocalizations.of(context)!.forward,
              style: TextStyle(fontSize: 15),
            ),
          ]),
        ),
        Expanded(
          child: Row(
            children: [
              Radio<String>(
                value: AppLocalizations.of(context)!.reverse,
                groupValue: radioButtonValue,
                onChanged: (val) {
                  setState(() {
                    radioButtonValue = val!;
                  });
                },
              ),
              Text(
                AppLocalizations.of(context)!.reverse,
                style: TextStyle(fontSize: 15),
              ),
            ],
          ),
        ),
      ]),
    );
  }

  Widget buttonsField() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 16, bottom: 12, right: 12, left: 12),
          child: Row(children: [
            Expanded(
              child: UnviredElevatedButton(
                  buttonBorderRadius: BorderRadius.circular(16.0),
                  text: Text(
                    AppLocalizations.of(context)!.count,
                    style: TextStyle(color: AppColor.white),
                  ),
                  onPressCallBack: () {
                    _onCountButton();
                  }),
            ),
            SizedBox(
              width: 4,
            ),
            Expanded(
              child: UnviredElevatedButton(
                  buttonBorderRadius: BorderRadius.circular(16.0),
                  text: Text(
                    AppLocalizations.of(context)!.reCount,
                    style: TextStyle(color: AppColor.white),
                  ),
                  onPressCallBack: () {
                    _onRecountButton();
                  }),
            )
          ]),
        ),
        Padding(
          padding: EdgeInsets.only(top: 8),
          child: Center(
              child: UnviredElevatedButton(
                  containerWidth: 80,
                  textPadding: EdgeInsets.zero,
                  text: Text(
                    AppLocalizations.of(context)!.exit,
                    style: TextStyle(color: AppColor.white),
                  ),
                  onPressCallBack: () {
                    Navigator.pushNamed(context, HomePage.routeName);
                  })),
        )
      ],
    );
  }

  void _initAndSetAppBar() async {
    BREWERY_HEADER? header = await DBHelper().getBreweryHeader();
    if (header != null) {
      appBar = header.brewery_desc.toString();
      setState(() {});
    }
  }

  void _initBreweryAreaSpinnerValues() async {
    breweryAreaSpinnerValues = await DBHelper().getAllAreas();
    breweryAreaSpinnerValues.insert(0, Constants.DEFAULT);
    dropdownBreweryValue = breweryAreaSpinnerValues.first;
  }

  Future<List<String>> _initAreaSelectionSpinnerValues(String area) async {
    List<String> allAreaSelection = [];
    List<String> allSections = await DBHelper().getAllSections(area);
    allSections.insert(0, Constants.DEFAULT);
    allAreaSelection = allSections;
    if (area == allAreaSelection.first) {
      return allAreaSelection = [];
    } else {
      return allAreaSelection;
    }
  }

  void _onCountButton() async {
    if (!validDataEntered()) {
      return;
    }
    final String area = dropdownBreweryValue;
    final String section = dropdownAreaSelectionValue.split("&").first;
    final String direction = radioButtonValue.substring(0, 1);
    final String selectionSequence = sequence;
    CountPageIntent countPageIntent = CountPageIntent(area: area, section: section, direction: direction, sequence: selectionSequence);
    if (await DBHelper().getCountOfStockCount(area, section, direction) > 0) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return UnviredConfirmationDialog(
                title: AppLocalizations.of(context)!.info_clear_for_new_count,
                titleStyle: UIHelper().dialogTitleTextStyle(),
                positiveActionLabel: AppLocalizations.of(context)!.no,
                onPositiveClickListener: () => Navigator.pop(context),
                negativeActionLabel: AppLocalizations.of(context)!.yes,
                onNegativeClickListener: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, CountPage.routeName, arguments: countPageIntent).then((value) {
                    setState(() {
                      dropdownBreweryValue = breweryAreaSpinnerValues.first;
                    });
                  });
                });
          });
    } else {
      Navigator.pushNamed(context, CountPage.routeName, arguments: countPageIntent);
      setState(() {
        dropdownBreweryValue = breweryAreaSpinnerValues.first;
      });
    }
  }

  bool validDataEntered() {
    if (dropdownBreweryValue.isEmpty || dropdownBreweryValue == breweryAreaSpinnerValues.first) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return UnviredConfirmationDialog(
                title: AppLocalizations.of(context)!.info_select_area,
                titleStyle: UIHelper().dialogTitleTextStyle(),
                positiveActionLabel: AppLocalizations.of(context)!.ok,
                onPositiveClickListener: () => Navigator.pop(context));
          });
      return false;
    }
    if (dropdownAreaSelectionValue.isEmpty || dropdownAreaSelectionValue == areaSelectionValues.first) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return UnviredConfirmationDialog(
                title: AppLocalizations.of(context)!.info_select_section,
                titleStyle: UIHelper().dialogTitleTextStyle(),
                positiveActionLabel: AppLocalizations.of(context)!.ok,
                onPositiveClickListener: () => Navigator.pop(context));
          });
      return false;
    }
    return true;
  }

  void _onRecountButton() async {
    if (!validDataEntered()) {
      return;
    }
    final String area = dropdownBreweryValue;
    final String section = dropdownAreaSelectionValue.split("&").first;
    final String direction = radioButtonValue.substring(0, 1);
    if (await DBHelper().getCountOfStockCount(area, section, direction) > 0) {
      String msg = AppLocalizations.of(context)!.info_clear_for_recount;
      msg = msg.replaceAll("area", area);
      msg = msg.replaceAll("section", section);
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return UnviredConfirmationDialog(
                title: msg,
                titleStyle: UIHelper().dialogTitleTextStyle(),
                positiveActionLabel: AppLocalizations.of(context)!.no,
                onPositiveClickListener: () => Navigator.pop(context),
                negativeActionLabel: AppLocalizations.of(context)!.yes,
                onNegativeClickListener: () async {
                  UIHelper().showCustomToast(context, AppLocalizations.of(context)!.clearingExistingCounts);
                  // showUnviredToast(context, message: AppLocalizations.of(context)!.clearingExistingCounts);
                  await DBHelper().clearRecountStockCounts(area, section, direction);
                  CountPageIntent countPageIntent = CountPageIntent(area: area, section: section, direction: direction, sequence: sequence);
                  Navigator.pushNamed(context, CountPage.routeName, arguments: countPageIntent).then((value) {
                    setState(() {
                      dropdownBreweryValue = breweryAreaSpinnerValues.first;
                    });
                  });
                });
          });
    } else {
      CountPageIntent countPageIntent = CountPageIntent(area: area, section: section, direction: direction, sequence: sequence);
      Navigator.pushNamed(context, CountPage.routeName, arguments: countPageIntent).then((value) {
        setState(() {
          dropdownBreweryValue = breweryAreaSpinnerValues.first;
        });
      });
    }
  }
}

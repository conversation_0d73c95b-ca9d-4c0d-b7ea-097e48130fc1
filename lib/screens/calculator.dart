import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:nsts/utils/constants.dart';
import 'package:unvired_ui/unvired_ui.dart';

import '../helper/ui_helper.dart';
import '../utils/app_colors.dart';

class Calculator extends StatefulWidget {
  static const routeName = '/calculator';
  final String requestcode;
  const Calculator({Key? key, required this.requestcode}) : super(key: key);

  @override
  State<Calculator> createState() => _CalculatorState();
}

class _CalculatorState extends State<Calculator> {
  TextEditingController calculatorController = TextEditingController(text: '0');
  var result = '0';
  String equation = '';
  double valueOne = double.nan;
  double valueTwo = 0;
  static const String ADDITION = '+';
  static const String SUBTRACTION = '-';
  static const String MULTIPLICATION = '*';
  static const String DIVISION = '/';
  static String CURRENT_ACTION = '';
  double finalValue = 0;
  bool calculationPerformed = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      alignment: Alignment.bottomRight,
                      margin: EdgeInsets.only(right: 8, bottom: 30),
                      child: Text(
                        result,
                        style: TextStyle(color: AppColor.black, fontSize: 30),
                      ),
                    ),
                    UnviredTextField(
                      controller: calculatorController,
                      textAlign: TextAlign.end,
                      style: TextStyle(color: AppColor.blue, fontSize: 20),
                      enableBorder: UnderlineInputBorder(borderSide: BorderSide(color: AppColor.grey)),
                      onTap: () {},
                      readOnly: true,
                    ),
                  ],
                ),
              ),
              calculatorField()
            ],
          ),
        ),
      ),
    );
  }

  Widget calculatorField() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 4.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                  onPressed: () {
                    setState(() {
                      calculatorController.text += AppLocalizations.of(context)!.seven;
                      equation += '7';
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.seven,
                    style: buttonTextStyle(),
                  )),
              TextButton(
                  onPressed: () {
                    setState(() {
                      calculatorController.text += AppLocalizations.of(context)!.eight;
                      equation += '8';
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.eight,
                    style: buttonTextStyle(),
                  )),
              TextButton(
                  onPressed: () {
                    setState(() {
                      calculatorController.text += AppLocalizations.of(context)!.nine;
                      equation += '9';
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.nine,
                    style: buttonTextStyle(),
                  )),
              TextButton(
                  onPressed: () {
                    setState(() {
                      computeCalculation();
                      CURRENT_ACTION = DIVISION;
                      equation += '/';
                      // result = '${_formatingToDouble(equation)}';
                      result = equation;
                      calculatorController.text = '';
                      calculationPerformed = false;
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.division,
                    style: buttonTextStyle(color: AppColor.blue),
                  ))
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 4.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                  onPressed: () {
                    setState(() {
                      calculatorController.text += AppLocalizations.of(context)!.four;
                      equation += '4';
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.four,
                    style: buttonTextStyle(),
                  )),
              TextButton(
                  onPressed: () {
                    setState(() {
                      calculatorController.text += AppLocalizations.of(context)!.five;
                      equation += '5';
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.five,
                    style: buttonTextStyle(),
                  )),
              TextButton(
                  onPressed: () {
                    setState(() {
                      calculatorController.text += AppLocalizations.of(context)!.six;
                      equation += '6';
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.six,
                    style: buttonTextStyle(),
                  )),
              TextButton(
                  onPressed: () {
                    setState(() {
                      computeCalculation();
                      CURRENT_ACTION = MULTIPLICATION;
                      equation += '*';
                      result = equation;
                      calculatorController.text = '';
                      calculationPerformed = false;
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.multiply,
                    style: buttonTextStyle(color: AppColor.blue),
                  ))
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 4),
          child: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
            TextButton(
                onPressed: () {
                  setState(() {
                    calculatorController.text += AppLocalizations.of(context)!.one;
                    equation += '1';
                  });
                },
                child: Text(
                  AppLocalizations.of(context)!.one,
                  style: buttonTextStyle(),
                )),
            TextButton(
                onPressed: () {
                  setState(() {
                    calculatorController.text += AppLocalizations.of(context)!.two;
                    equation += '2';
                  });
                },
                child: Text(
                  AppLocalizations.of(context)!.two,
                  style: buttonTextStyle(),
                )),
            TextButton(
                onPressed: () {
                  setState(() {
                    calculatorController.text += AppLocalizations.of(context)!.three;
                    equation += '3';
                  });
                },
                child: Text(
                  AppLocalizations.of(context)!.three,
                  style: buttonTextStyle(),
                )),
            TextButton(
                onPressed: () {
                  setState(() {
                    computeCalculation();
                    CURRENT_ACTION = SUBTRACTION;
                    equation += '-';
                    result = equation;
                    calculatorController.text = '';
                    calculationPerformed = false;
                  });
                },
                child: Text(
                  AppLocalizations.of(context)!.substraction,
                  style: buttonTextStyle(color: AppColor.blue),
                ))
          ]),
        ),
        Padding(
          padding: EdgeInsets.only(top: 4.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                  onPressed: () {
                    setState(() {
                      calculatorController.text += AppLocalizations.of(context)!.dot;
                      equation += '.';
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.dot,
                    style: buttonTextStyle(),
                  )),
              TextButton(
                  onPressed: () {
                    setState(() {
                      calculatorController.text += AppLocalizations.of(context)!.zero;
                      equation += '0';
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.zero,
                    style: buttonTextStyle(),
                  )),
              TextButton(
                  onPressed: () {
                    setState(() {
                      if (calculatorController.text.length > 0) {
                        calculatorController.text = calculatorController.text.substring(0, calculatorController.text.length - 1);
                        equation = equation.substring(0, equation.length - 1);
                      } else {
                        valueOne = double.nan;
                        valueTwo = double.nan;
                        finalValue = 0;
                        calculatorController.text = '';
                        equation = '';
                        result = '';
                      }
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.c,
                    style: buttonTextStyle(color: AppColor.blue),
                  )),
              TextButton(
                  onPressed: () {
                    setState(() {
                      computeCalculation();
                      CURRENT_ACTION = ADDITION;
                      equation += '+';
                      result = equation;
                      calculatorController.text = '';
                      calculationPerformed = false;
                    });
                  },
                  child: Text(
                    AppLocalizations.of(context)!.addition,
                    style: buttonTextStyle(color: AppColor.blue),
                  ))
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 8),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: UnviredElevatedButton(
                    buttonBorderRadius: BorderRadius.circular(16),
                    text: Text(
                      AppLocalizations.of(context)!.setValue,
                      style: TextStyle(fontSize: 20),
                    ),
                    onPressCallBack: () {
                      checkAndSetValue();
                    }),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(right: 2),
                  child: TextButton(
                      onPressed: () {
                        if (calculationPerformed) {
                          return;
                        }
                        computeCalculation();
                        String finalResult = equation;
                        finalResult += ' = ';
                        finalResult += _formatingToDouble(valueOne);
                        if (finalResult.contains("Infinity")) {
                          var val = finalResult.replaceAll("Infinity", "0");
                          finalResult = val;
                        }
                        setState(() {
                          result = finalResult;
                          CURRENT_ACTION = '';
                          calculationPerformed = true;
                          equation = valueOne.toInt().toString();
                        });
                      },
                      child: Text(
                        AppLocalizations.of(context)!.equal,
                        style: buttonTextStyle(color: AppColor.blue),
                      )),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatingToDouble(double value) {
    var verbose = value.toStringAsFixed(2);
    var trimmed = verbose;
    for (var i = verbose.length - 1; i > 0; i--) {
      if (trimmed[i] != '0' && trimmed[i] != '.' || !trimmed.contains('.')) {
        break;
      }
      trimmed = trimmed.substring(0, i);
    }
    return trimmed;
  }

  TextStyle buttonTextStyle({Color? color}) {
    return TextStyle(color: color ?? AppColor.black, fontSize: 20);
  }

  void computeCalculation() {
    if (valueOne.isNaN) {
      if (calculatorController.text.isNotEmpty) {
        double val = double.parse(calculatorController.text);
        valueOne = val;
      } else {
        valueOne = double.nan;
      }
    } else {
      Parser p = Parser();
      Expression exp = p.parse(calculatorController.text);
      ContextModel cm = ContextModel();
      double eval = exp.evaluate(EvaluationType.REAL, cm);
      valueTwo = eval;
      switch (CURRENT_ACTION) {
        case ADDITION:
          valueOne = this.valueOne + valueTwo;
          break;

        case SUBTRACTION:
          valueOne = this.valueOne - valueTwo;
          break;

        case MULTIPLICATION:
          valueOne = this.valueOne * valueTwo;
          break;

        case DIVISION:
          valueOne = this.valueOne / valueTwo;
          break;
      }

      //valueTwo = double.parse(calculatorController.text);
      // if (CURRENT_ACTION == ADDITION) {
      //   valueOne = valueOne + valueTwo;
      // } else if (CURRENT_ACTION == SUBTRACTION) {
      //   valueOne = valueOne - valueTwo;
      // } else if (CURRENT_ACTION == MULTIPLICATION) {
      //   valueOne = valueOne * valueTwo;
      // } else if (CURRENT_ACTION == DIVISION) {
      //   valueOne = valueOne / valueTwo;
      // }
    }
    finalValue = valueOne;
  }

  void checkAndSetValue() {
    if (finalValue < 0 || finalValue.isNaN || finalValue.isInfinite) {
      return UIHelper().showCustomToast(context, AppLocalizations.of(context)!.invalidNumber);
// showUnviredToast(context,
// gravity: ToastGravity.CENTER,
// backGroundColor: AppColor.black,
// message: AppLocalizations.of(context)!.invalidNumber);
    }
    switch (widget.requestcode) {
      case Constants.CALCULATE_CODE_MAJ_UOM:
        if (finalValue != finalValue.toInt()) {
          confirmToSetDecimalValue();
          break;
        }
        finalResults(finalValue.toInt(), -1);
        break;

      case Constants.CALCULATE_CODE_MIN_UOM:
        finalResults(-1, finalValue);
        break;
    }
  }

  void confirmToSetDecimalValue() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return UnviredConfirmationDialog(
            title: AppLocalizations.of(context)!.info_decimal_value,
            titleStyle: UIHelper().dialogTitleTextStyle(),
            dialogActions: [
              TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    AppLocalizations.of(context)!.no,
                    style: TextStyle(color: AppColor.red),
                  )),
              TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    int majValue = finalValue.toInt();
                    finalResults(majValue, finalValue - majValue);
                  },
                  child: Text(AppLocalizations.of(context)!.yes, style: TextStyle(color: AppColor.primaryDark)))
            ],
          );
        });
  }

  void finalResults(int maj, double min) {
    List<ReturnDataOfCalculator> data = [];
    if (maj >= 0) {
      data.add(ReturnDataOfCalculator(majUom: maj.toString()));
    }
    if (min >= 0) {
      String minUom = _formatingToDouble(min);
      data.add(ReturnDataOfCalculator(minUom: minUom));
    }
    Navigator.pop(context, data);
  }
}

class ReturnDataOfCalculator {
  final String? majUom;
  final String? minUom;

  ReturnDataOfCalculator({this.majUom, this.minUom});
}

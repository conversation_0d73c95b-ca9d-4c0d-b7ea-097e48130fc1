import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/logger.dart';
import 'package:nsts/be/AREA_BIN_MAP.dart';
import 'package:nsts/be/MATERIAL_HEADER.dart';
import 'package:nsts/be/STOCK_COUNT_HEADER.dart';
import 'package:nsts/helper/db_helper.dart';
import 'package:nsts/models/countInfo.dart';
import 'package:nsts/screens/prepare_for_count_page.dart';
import 'package:nsts/widgets/ui_textstyle_helper.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:unvired_ui/unvired_ui.dart';

import '../be/STOCK_COUNT_ITEM.dart';
import '../helper/ui_helper.dart';
import '../utils/app_colors.dart';
import '../utils/constants.dart';
import '../utils/utils.dart';
import '../widgets/label_value_widget.dart';
import 'calculator.dart';

class CountPageIntent {
  final String area;
  final String section;
  final String direction;
  final String sequence;

  CountPageIntent(
      {required this.area,
      required this.section,
      required this.direction,
      required this.sequence});
}

class CountPage extends StatefulWidget {
  static const routeName = '/countPage';
  final CountPageIntent countPageIntent;
  const CountPage({Key? key, required this.countPageIntent}) : super(key: key);

  @override
  State<CountPage> createState() => _CountPageState();
}

class _CountPageState extends State<CountPage> {
  GlobalKey<FormState> formSKU = GlobalKey<FormState>();
  GlobalKey<FormState> formBatch = GlobalKey<FormState>();
  GlobalKey<FormState> formDate = GlobalKey<FormState>();
  GlobalKey<FormState> formMoj = GlobalKey<FormState>();
  GlobalKey<FormState> formMin = GlobalKey<FormState>();
  CountInfo? countInfo;
  bool isSplitButtonVisible = false;
  bool isSaveButtonEnable = false;
  String radioButton = 'Unrestricted';
  bool allowRandom = false;
  bool recordIsSplitBin = false;
  bool editBatch = false;
  bool editExp = false;
  bool batchCheck = true;
  bool expCheck = true;
  bool batchCheckEnable = true;
  bool expCheckEnable = true;
  bool countIndicatorVisible = false;
  bool binLabel = false;
  bool binValue = false;
  String countIndicator = '';
  STOCK_COUNT_ITEM? currentCountItem;
  String materialDesc = '';
  String bin = '';
  String majUomLabel = '';
  String minUomLabel = '';
  FocusNode skuFocus = FocusNode();
  FocusNode batchFocus = FocusNode();
  FocusNode expFocus = FocusNode();
  FocusNode majorFocus = FocusNode();
  FocusNode minorFocus = FocusNode();
  AREA_BIN_MAP? currentBin;
  TextEditingController skuController = TextEditingController();
  TextEditingController batchController = TextEditingController();
  TextEditingController expiryDateController = TextEditingController();
  TextEditingController majorUomController = TextEditingController();
  TextEditingController minorUomController = TextEditingController();
  int countID = 0;
  int? startOfSplittingLetter;
  String expiryDateError = '';
  bool onTapMajDisable = true;
  bool onTapSingleMajorDisable = true;
  bool onTapSingleMinorDisable = true;

  @override
  void initState() {
    getData();
    super.initState();
  }

  void getData() async {
    _initAndSetCountInfoData();
    if (countInfo == null) {
      showUnviredToast(context,
          message:
              AppLocalizations.of(context)!.somethingWentWrongPleaseTryAgain);
      Navigator.pushNamed(context, PrepareForCountPage.routeName);
    }
    initScreenButtonsEnable();
    await _initalizeIsCountFinished();
    await setScreenValues();
    startOfSplittingLetter = 97;
  }

  void _initAndSetCountInfoData() {
    countInfo = CountInfo(
        area: widget.countPageIntent.area,
        direction: widget.countPageIntent.direction,
        section: widget.countPageIntent.section,
        sequence: widget.countPageIntent.sequence);
  }

  void initScreenButtonsEnable() {
    if (Constants.SEQUENCE_ROUTE == widget.countPageIntent.sequence) {
      binLabel = true;
      binValue = true;
      isSplitButtonVisible = true;
    } else {
      binLabel = false;
      binValue = false;
      isSplitButtonVisible = false;
    }
  }

  Future<void> _initalizeIsCountFinished() async {
    allowRandom = await DBHelper().isCountFinished(countInfo!);
    if (allowRandom) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return UnviredConfirmationDialog(
              title: AppLocalizations.of(context)!.info_count_finished_recount,
              titleStyle: UIHelper().dialogTitleTextStyle(),
              positiveActionLabel: AppLocalizations.of(context)!.ok,
              onPositiveClickListener: () {
                Navigator.pop(context);
              },
            );
          });
      // List<STOCK_COUNT_ITEM> stockCountItems = await DBHelper().getCountItems();
      // if (stockCountItems.isNotEmpty) {
      //   currentCountItem = await DBHelper().getNextItem(countInfo!, 0);
      // } else {
      currentCountItem = null;
      isSaveButtonEnable = false;
      // }
    } else if (Constants.SEQUENCE_ROUTE == countInfo!.sequence.toString()) {
      currentCountItem = await DBHelper().getNextItem(countInfo!, 0);

      countIndicatorVisible = false;
      int totalCount = await DBHelper().getTotalFinishedCount(countInfo!);
      if (totalCount > 0) {
        countIndicatorVisible = true;
        int currentCount = 0;
        if (currentCountItem == null) {
          currentCount = totalCount + 1;
        } else {
          currentCount = await DBHelper()
              .getCountPosition(countInfo!, currentCountItem!.count_id!);
        }
        String result = "";
        result += currentCount.toString();
        result += "/";
        result += totalCount.toString();
        countIndicator = result;
      }
    } else {
      currentCountItem = null;
    }
  }

  Future<void> setScreenValues() async {
    MATERIAL_HEADER? materialHeader;
    radioButton = 'Unrestricted';
    // countIndicatorVisible = false;
    if (currentCountItem != null) {
      isSaveButtonEnable = true;
      // isSaveButtonEnable = false;
      materialHeader = await DBHelper()
          .getMaterialHeader(currentCountItem!.mat_no.toString());
      if (materialHeader != null) {
        skuController.text = materialHeader.mat_no.toString();
        materialDesc = materialHeader.description.toString();
        batchController.text = currentCountItem!.batch_no != null
            ? currentCountItem!.batch_no.toString()
            : '';
        expiryDateController.text = currentCountItem!.expiry_dt != null
            ? currentCountItem!.expiry_dt.toString()
            : '';
        int majorUom = currentCountItem!.major_unit_qty!.toInt();
        majorUomController.text = majorUom.toString();
        minorUomController.text = currentCountItem!.minor_unit_qty.toString();
      }
      switch (currentCountItem!.status) {
        case "1":
          radioButton = 'Unrestricted';
          break;
        case "2":
          radioButton = 'Quality';
          break;
        case "4":
          radioButton = 'Blocked';
          break;
      }
    } else if (allowRandom) {
      isSaveButtonEnable = false;
    } else {
      isSaveButtonEnable = true;
    }

    if (Constants.SEQUENCE_ROUTE == countInfo!.sequence.toString()) {
      recordIsSplitBin = false;
      if (currentCountItem != null) {
        currentBin = await DBHelper()
            .getBin(countInfo!, currentCountItem!.bin.toString());
      } else {
        currentBin = await DBHelper().getBin(countInfo!, '');
      }
      if (currentBin != null) {
        bin = currentBin!.bin.toString();
      } else {
        if (currentCountItem != null) {
          recordIsSplitBin = true;
          bin = currentCountItem!.bin.toString();
        } else {
          recordIsSplitBin = false;
          bin = "";
        }
      }
    }
    //FocusScope.of(context).requestFocus(skuFocus);
    // onChangeSku(skuController.text);
    if (materialHeader != null) {
      batchAndDateFieldEnables(materialHeader);
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          Navigator.pushNamed(context, PrepareForCountPage.routeName);
          return true;
        },
        child: Scaffold(
            body: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                firstCard(),
                secondCard(),
                SizedBox(height: 4),
                buttonsField()
              ],
            ),
          ),
        )));
  }

  Widget firstCard() {
    return Card(
      elevation: 4,
      margin: EdgeInsets.only(right: 2, left: 2, top: 2),
      child: Container(
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.all(2),
        child: Padding(
          padding: const EdgeInsets.only(left: 4.0, right: 4.0),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Row(children: [
              Expanded(
                child: LabelValueWidget(
                  label: AppLocalizations.of(context)!.area,
                  valueWidget: widget.countPageIntent.area,
                ),
              ),
              Expanded(
                child: LabelValueWidget(
                  label: binLabel ? AppLocalizations.of(context)!.bin : "",
                  valueWidget: binLabel ? bin : "",
                ),
              ),
            ]),
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(children: [
                Expanded(
                  child: LabelValueWidget(
                    label: AppLocalizations.of(context)!.section,
                    valueWidget: widget.countPageIntent.section,
                  ),
                ),
                Expanded(
                  child: LabelValueWidget(
                    label: AppLocalizations.of(context)!.direction,
                    valueWidget: widget.countPageIntent.direction,
                  ),
                ),
              ]),
            )
          ]),
        ),
      ),
    );
  }

  Widget secondCard() {
    return Card(
      elevation: 4,
      margin: EdgeInsets.only(right: 2, left: 2, top: 2),
      child: Container(
          width: double.infinity,
          margin: EdgeInsets.only(right: 2, left: 2, top: 2),
          child: Padding(
            padding: EdgeInsets.only(left: 6, right: 6),
            child: Column(children: [
              Padding(
                padding: const EdgeInsets.only(top: 2),
                child: Form(key: formSKU, child: skuField()),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Form(key: formBatch, child: batchField()),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Form(key: formDate, child: expiryDateField()),
              ),
              Row(children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(right: 60),
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Form(key: formMoj, child: majorUomField()),
                          ),
                          Form(
                              key: formMin,
                              child: Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: minorUomField(),
                              )),
                        ]),
                  ),
                ),
                Container(
                  height: 48,
                  margin: EdgeInsets.only(right: 8),
                  child: Visibility(
                    visible: countIndicatorVisible,
                    child: Center(
                      child: Text(
                        countIndicator,
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 25),
                      ),
                    ),
                  ),
                )
              ]),
              radioButtonsField()
            ]),
          )),
    );
  }

  Widget buttonsField() {
    return Padding(
      padding: const EdgeInsets.all(2),
      child: Row(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    UIHelper().getElevatedButton(
                        context: context,
                        buttonName: Icon(Icons.fast_rewind),
                        onPressCallBack: () {
                          _onPrevButton();
                        }),
                    SizedBox(width: 4),
                    UIHelper().getElevatedButton(
                        context: context,
                        buttonName: Icon(Icons.fast_forward),
                        onPressCallBack: () {
                          _onNextButton();
                        }),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Row(
                    children: [
                      UIHelper().getElevatedButton(
                          context: context,
                          buttonName: Text(
                              AppLocalizations.of(context)!.recountSKU,
                              style: TextStyle(fontSize: 14)),
                          onPressCallBack: () {
                            _onRecountButton();
                          }),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 4),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    isSplitButtonVisible
                        ? UIHelper().getElevatedButton(
                            context: context,
                            buttonName: Text(
                                AppLocalizations.of(context)!.splitBin,
                                style: TextStyle(fontSize: 14)),
                            onPressCallBack: isSplitButtonVisible
                                ? () {
                                    _onSplitButton();
                                  }
                                : () {},
                          )
                        : Container(height: 48)
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Row(
                    children: [
                      UIHelper().getElevatedButton(
                          context: context,
                          buttonName: Text(
                              AppLocalizations.of(context)!.changeSection,
                              style: TextStyle(fontSize: 14)),
                          onPressCallBack: () {
                            _onChangeSelectionButton();
                          }),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 4),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    UIHelper().getElevatedButton(
                      context: context,
                      buttonBackgroundColor: isSaveButtonEnable
                          ? MaterialStateProperty.all(AppColor.primaryDark)
                          : MaterialStateProperty.all(AppColor.grey),
                      buttonName: Text(AppLocalizations.of(context)!.save,
                          style: TextStyle(fontSize: 14)),
                      onPressCallBack: isSaveButtonEnable
                          ? () {
                              if (skuController.text.isNotEmpty) {
                                _onSaveButton();
                              } else {
                                showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (context) {
                                      return UnviredConfirmationDialog(
                                        title: AppLocalizations.of(context)!
                                            .info_for_empty_bin,
                                        titleStyle:
                                            UIHelper().dialogTitleTextStyle(),
                                        positiveActionLabel:
                                            AppLocalizations.of(context)!.no,
                                        onPositiveClickListener: () {
                                          Navigator.pop(context);
                                        },
                                        negativeActionLabel:
                                            AppLocalizations.of(context)!.yes,
                                        onNegativeClickListener: () {
                                          Navigator.pop(context);
                                          _onSaveCount();
                                        },
                                      );
                                    });
                              }
                            }
                          : () {},
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Row(
                    children: [
                      UIHelper().getElevatedButton(
                          context: context,
                          buttonName: Text(AppLocalizations.of(context)!.exit,
                              style: TextStyle(fontSize: 14)),
                          onPressCallBack: () {
                            Navigator.pushNamed(
                                context, PrepareForCountPage.routeName);
                          }),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget skuField() {
    return Row(children: [
      Container(
        width: 115,
        child: UnviredTextField(
          style: UIHelper().textFieldTextStyle(),
          controller: skuController,
          contentPadding: EdgeInsets.zero,
          onTap: () {},
          focusNode: skuFocus,
          onChange: (val) {
            TextSelection previousCursorSelection = skuController.selection;
            setState(() {
              skuController.text = val;
            });
            skuController.selection = previousCursorSelection;
            // onChangeSku(val);
          },
          onSubmit: (val) {
            onChangeSku(val, skuFocus);
          },
          readOnly: false,
          label: AppLocalizations.of(context)!.sku,
          keyBoardInputType: TextInputType.number,
          enableBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColor.grey, width: 1)),
          focusBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColor.blue, width: 2)),
          validate: validateSku,
        ),
      ),
      Expanded(
        child: Padding(
          padding: const EdgeInsets.only(left: 8),
          child: Text(materialDesc,
              style: UITextStyleHelper.textStyleLarge2Black(context)),
        ),
      )
    ]);
  }

  void onChangeSku(String sku, FocusNode focusNode) async {
    onTapSingleMajorDisable = false;
    onTapSingleMinorDisable = false;
    MATERIAL_HEADER? materialHeader;

    FocusNode? focusToRequest;

    if (sku.isNotEmpty) {
      materialHeader = await DBHelper().getMaterialHeaderForInput(sku);
    }

    if (materialHeader == null) {
      materialDesc = sku.isNotEmpty ? "****" : "";
      editBatch = true;
      batchCheckEnable = true;
      batchCheck = true;

      editExp = true;
      expCheckEnable = true;
      expCheck = true;

      setState(() {});
      focusToRequest = majorFocus;
    } else {
      skuController.text = materialHeader.mat_no.toString();
      materialDesc = materialHeader.description.toString();

      if (Constants.X == materialHeader.batch_req) {
        if (focusNode == skuFocus || focusNode == batchFocus) {
          editBatch = false;
          batchCheckEnable = false;
          batchCheck = false;
          focusToRequest ??= batchFocus;
        }
      } else {
        if (focusNode == skuFocus || focusNode == batchFocus) {
          batchController.text = "";
          editBatch = true;
          batchCheckEnable = true;
          batchCheck = true;
          focusToRequest ??= majorFocus;
        }
      }

      if (Constants.X == materialHeader.expiry_req) {
        if (focusNode == skuFocus || focusNode == expFocus) {
          editExp = false;
          expCheckEnable = false;
          expCheck = false;
          focusToRequest ??= expFocus;
        }
      } else {
        if (focusNode == skuFocus || focusNode == expFocus) {
          expiryDateController.text = "";
          editExp = true;
          expCheckEnable = true;
          expCheck = true;
        }
      }

      if (materialHeader.major_uom!.isEmpty) {
        majUomLabel = AppLocalizations.of(context)!.majUom;
        focusToRequest ??= majorFocus;
      } else {
        majUomLabel = materialHeader.major_uom.toString();
      }

      if (materialHeader.minor_uom!.isEmpty) {
        minUomLabel = AppLocalizations.of(context)!.minUom;
        focusToRequest ??= minorFocus;
      } else {
        minUomLabel = materialHeader.minor_uom.toString();
      }

      setState(() {});
    }

    if (focusToRequest != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        FocusScope.of(context).requestFocus(focusToRequest!);
      });
    }
  }

/*  void onChangeSku(String sku) async {
    onTapSingleMajorDisable = false;
    onTapSingleMinorDisable = false;
    MATERIAL_HEADER? materialHeader;
    if (sku.isNotEmpty) {
      materialHeader = await DBHelper().getMaterialHeaderForInput(sku);
    }
    if (materialHeader == null) {
      materialDesc = sku.isNotEmpty ? "****" : "";
      editBatch = true;
      batchCheckEnable = true;
      batchCheck = true;

      editExp = true;
      expCheckEnable = true;
      expCheck = true;
      setState(() {});
      WidgetsBinding.instance.addPostFrameCallback((_) {
        FocusScope.of(context).requestFocus(majorFocus);
      });

      return;
    }
    skuController.text = materialHeader.mat_no.toString();
    materialDesc = materialHeader.description.toString();

    // batchAndDateFieldEnables(materialHeader);
    if (Constants.X == materialHeader.batch_req) {
      setState(() {
        editBatch = false;
        batchCheckEnable = false;
        batchCheck = false;
      });
      WidgetsBinding.instance.addPostFrameCallback((_) {
        FocusScope.of(context).requestFocus(batchFocus);
      });
    } else {
      setState(() {
        batchController.text = "";
        editBatch = true;
        batchCheckEnable = true;
        batchCheck = true;
      });
      WidgetsBinding.instance.addPostFrameCallback((_) {
        FocusScope.of(context).requestFocus(majorFocus);
      });
    }

    if (Constants.X == materialHeader.expiry_req) {
      setState(() {
        editExp = false;
        expCheckEnable = false;
        expCheck = false;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          FocusScope.of(context).requestFocus(expFocus);
        });
      });
    } else {
      setState(() {
        expiryDateController.text = "";
        editExp = true;
        expCheckEnable = true;
        expCheck = true;
      });
    }

    if (materialHeader.major_uom!.isEmpty) {
      setState(() {
        majUomLabel = AppLocalizations.of(context)!.majUom;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          FocusScope.of(context).requestFocus(majorFocus);
        });
      });
    } else {
      setState(() {
        majUomLabel = materialHeader!.major_uom.toString();
      });
    }
    if (materialHeader.minor_uom!.isEmpty) {
      setState(() {
        minUomLabel = AppLocalizations.of(context)!.minUom;
      });

      WidgetsBinding.instance.addPostFrameCallback((_) {
        FocusScope.of(context).requestFocus(minorFocus);
      });
    } else {
      setState(() {
        minUomLabel = materialHeader!.minor_uom.toString();
      });
    }
  }*/

  void batchAndDateFieldEnables(MATERIAL_HEADER materialHeader) {
    FocusNode? focusToRequest;
    if (Constants.X == materialHeader.batch_req) {
      editBatch = false;
      batchCheckEnable = false;
      batchCheck = false;
      focusToRequest ??= batchFocus;
    } else {
      batchController.text = "";
      editBatch = true;
      batchCheckEnable = true;
      batchCheck = true;
      focusToRequest ??= majorFocus;
    }

    if (Constants.X == materialHeader.expiry_req) {
      editExp = false;
      expCheckEnable = false;
      expCheck = false;
      focusToRequest ??= expFocus;
    } else {
      expiryDateController.text = "";
      editExp = true;
      expCheckEnable = true;
      expCheck = true;
    }

    setState(() {});

    if (focusToRequest != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        FocusScope.of(context).requestFocus(focusToRequest!);
      });
    }
  }

  Widget batchField() {
    return Row(
      children: [
        Expanded(
          child: Container(
            // height: 60,
            child: UnviredTextField(
              controller: batchController,
              contentPadding: EdgeInsets.zero,
              style: editBatch
                  ? UIHelper().textFieldTextStyle(textColor: AppColor.grey)
                  : UIHelper().textFieldTextStyle(),
              onTap: editBatch
                  ? () {
                      batchFocus.unfocus();
                    }
                  : () {
                      onChangeSku(skuController.text, batchFocus);
                    },
              onChange: (val) {
                TextSelection previousCursorSelection =
                    batchController.selection;
                batchController.text = val;
                batchController.selection = previousCursorSelection;
                batchCheckEnable = false;
              },
              onSubmit: (val) {
                FocusScope.of(context).requestFocus(majorFocus);
              },
              readOnly: editBatch,
              focusNode: batchFocus,
              label: AppLocalizations.of(context)!.batch,
              keyBoardInputType: TextInputType.number,
              enableBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColor.grey, width: 1)),
              focusBorder: editBatch
                  ? UnderlineInputBorder(
                      borderSide: BorderSide(color: AppColor.grey, width: 1))
                  : UnderlineInputBorder(
                      borderSide: BorderSide(color: AppColor.blue, width: 2)),
              validate: validateBatch,
            ),
          ),
        ),
        Container(
          margin: EdgeInsets.only(top: 8, left: 8, right: 8),
          child: Row(children: [
            Container(
              width: 30,
              child: batchCheckEnable
                  ? Checkbox(
                      value: batchCheck,
                      activeColor: AppColor.grey,
                      onChanged: (value) {},
                    )
                  : Checkbox(
                      value: batchCheck,
                      onChanged: (value) {
                        if (value == true) {
                          batchCheck = value!;
                          batchController.text = '';
                          editBatch = true;
                          batchFocus.unfocus();
                          onTapMajDisable = false;
                        } else {
                          batchCheck = value!;
                          editBatch = false;
                        }
                        setState(() {});
                      },
                    ),
            ),
            Text(
              AppLocalizations.of(context)!.na,
              style: TextStyle(color: AppColor.black),
            )
          ]),
        ),
      ],
    );
  }

  Widget expiryDateField() {
    return Row(
      children: [
        Expanded(
          child: Container(
            // height: 60,
            child: UnviredTextField(
              controller: expiryDateController,
              contentPadding: EdgeInsets.zero,
              style: editExp
                  ? UIHelper().textFieldTextStyle(textColor: AppColor.grey)
                  : UIHelper().textFieldTextStyle(),
              onTap: editExp
                  ? () {
                      expFocus.unfocus();
                    }
                  : () {
                      onChangeSku(skuController.text, expFocus);
                    },
              onChange: (val) {
                TextSelection previousCursorSelection =
                    expiryDateController.selection;
                expiryDateController.text = val;
                expiryDateController.selection = previousCursorSelection;
                expCheckEnable = false;
              },
              onSubmit: (val) {
                FocusScope.of(context).requestFocus(majorFocus);
              },
              focusNode: expFocus,
              readOnly: editExp,
              label: AppLocalizations.of(context)!.expiryDate,
              keyBoardInputType: TextInputType.number,
              enableBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColor.grey)),
              focusBorder: editExp
                  ? UnderlineInputBorder(
                      borderSide: BorderSide(color: AppColor.grey))
                  : UnderlineInputBorder(
                      borderSide: BorderSide(color: AppColor.blue)),
              validate: validateDate,
            ),
          ),
        ),
        Container(
            margin: EdgeInsets.only(top: 8, left: 8, right: 8),
            child: Row(children: [
              Container(
                width: 30,
                child: expCheckEnable
                    ? Checkbox(
                        value: expCheck,
                        activeColor: AppColor.grey,
                        onChanged: (value) {},
                      )
                    : Checkbox(
                        value: expCheck,
                        onChanged: (value) {
                          if (value == true) {
                            expCheck = value!;
                            expiryDateController.text = '';
                            editExp = true;
                            expFocus.unfocus();
                            onTapMajDisable = false;
                          } else {
                            expCheck = value!;
                            editExp = false;
                          }
                          setState(() {});
                        },
                      ),
              ),
              Text(
                AppLocalizations.of(context)!.na,
                style: TextStyle(color: AppColor.black),
              )
            ]))
      ],
    );
  }

  Widget majorUomField() {
    return Row(children: [
      Expanded(
        child: Container(
          // height: 60,
          child: UnviredTextField(
            controller: majorUomController,
            onTap: onTapMajDisable
                ? () {
                    if (skuController.text.isNotEmpty) {
                      onTapSingleMajorDisable
                          ? onChangeSku(skuController.text, majorFocus)
                          : () {};
                    }
                  }
                : () {},
            contentPadding: EdgeInsets.zero,
            style: UIHelper().textFieldTextStyle(),
            onChange: (val) {
              TextSelection previousCursorSelection =
                  majorUomController.selection;
              majorUomController.text = val;
              majorUomController.selection = previousCursorSelection;
            },
            onSubmit: (val) {
              FocusScope.of(context).requestFocus(minorFocus);
            },
            readOnly: false,
            keyBoardInputType: TextInputType.number,
            focusNode: majorFocus,
            label: majUomLabel.isNotEmpty
                ? majUomLabel
                : AppLocalizations.of(context)!.majUom,
            enableBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: AppColor.grey, width: 1)),
            focusBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: AppColor.blue, width: 2)),
            validate: validateMOJ,
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: GestureDetector(
          child: Container(
            width: 40,
            height: 40,
            margin: EdgeInsets.all(8),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.0),
                color: AppColor.primaryDark),
            child: Padding(
              padding: EdgeInsets.all(8),
              child: Image.asset('assets/images/ic_calculator.png',
                  color: AppColor.white),
            ),
          ),
          onTap: () async {
            var majUom = await Navigator.pushNamed(
                context, Calculator.routeName,
                arguments: Constants.CALCULATE_CODE_MAJ_UOM);
            List<ReturnDataOfCalculator>? data =
                majUom as List<ReturnDataOfCalculator>;
            setState(() {
              majorUomController.text = data[0].majUom.toString();
              if (data[1].majUom.toString().isNotEmpty) {
                minorUomController.text = data[1].minUom.toString();
              }
            });
          },
        ),
      )
    ]);
  }

  // getOnTapMajFunction() {
  //   // if (editBatch && !expCheck) {
  //   //   return () {};
  //   // } else {
  //   if (onTapMajDisable) {
  //     () {
  //       if (skuController.text.isNotEmpty) {
  //         return onChangeSku(skuController.text);
  //       }
  //     };
  //   } else {
  //     return () {};
  //   }
  //   // }
  // }

  Widget minorUomField() {
    return Row(children: [
      Expanded(
        child: Container(
          // height: 60,
          child: UnviredTextField(
            controller: minorUomController,
            contentPadding: EdgeInsets.zero,
            onTap: onTapMajDisable
                ? () {
                    if (skuController.text.isNotEmpty) {
                      onTapSingleMinorDisable
                          ? onChangeSku(skuController.text, minorFocus)
                          : () {};
                    }
                  }
                : () {},
            style: UIHelper().textFieldTextStyle(),
            onChange: (val) {
              TextSelection previousCursorSelection =
                  minorUomController.selection;
              minorUomController.text = val;
              minorUomController.selection = previousCursorSelection;
            },
            readOnly: false,
            focusNode: minorFocus,
            label: minUomLabel.isNotEmpty
                ? minUomLabel
                : AppLocalizations.of(context)!.minUom,
            keyBoardInputType: TextInputType.number,
            enableBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: AppColor.grey, width: 1)),
            focusBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: AppColor.blue, width: 2)),
            validate: validateMIN,
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: GestureDetector(
          child: Container(
            width: 40,
            height: 40,
            margin: EdgeInsets.only(right: 8, left: 8, top: 8, bottom: 8),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.0),
                color: AppColor.primaryDark),
            child: Padding(
              padding: EdgeInsets.all(8),
              child: Image.asset('assets/images/ic_calculator.png',
                  color: AppColor.white),
            ),
          ),
          onTap: () async {
            var minUom = await Navigator.pushNamed(
                context, Calculator.routeName,
                arguments: Constants.CALCULATE_CODE_MIN_UOM);
            List<ReturnDataOfCalculator>? data =
                minUom as List<ReturnDataOfCalculator>;
            setState(() {
              minorUomController.text = data[0].minUom.toString();
            });
          },
        ),
      )
    ]);
  }

  Widget radioButtonsField() {
    return Row(mainAxisAlignment: MainAxisAlignment.start, children: [
      Row(mainAxisAlignment: MainAxisAlignment.start, children: [
        SizedBox(
          width: 27,
          child: Radio<String>(
            value: AppLocalizations.of(context)!.unrestricted,
            groupValue: radioButton,
            onChanged: (val) {
              setState(() {
                radioButton = val!;
              });
            },
          ),
        ),
        Text(
          AppLocalizations.of(context)!.unrestricted,
          style: TextStyle(fontSize: 16, color: AppColor.black),
        ),
      ]),
      SizedBox(width: 30),
      Expanded(
          child: Row(
        children: [
          SizedBox(
            width: 27,
            child: Radio<String>(
              value: AppLocalizations.of(context)!.quality,
              groupValue: radioButton,
              onChanged: (val) {
                setState(() {
                  radioButton = val!;
                });
              },
            ),
          ),
          Text(
            AppLocalizations.of(context)!.quality,
            style: TextStyle(fontSize: 16, color: AppColor.black),
          ),
        ],
      )),
      Expanded(
          child: Row(children: [
        SizedBox(
            width: 27,
            child: Radio<String>(
              value: AppLocalizations.of(context)!.blocked,
              groupValue: radioButton,
              onChanged: (val) {
                setState(() {
                  radioButton = val!;
                });
              },
            )),
        Text(
          AppLocalizations.of(context)!.blocked,
          style: TextStyle(fontSize: 16, color: AppColor.black),
        ),
      ])),
    ]);
  }

  void _onPrevButton() async {
    if (await DBHelper().getCountOfStockCount(
            countInfo!.area, countInfo!.section, countInfo!.direction) >
        0) {
      loadPrevCount();
    }
  }

  void loadPrevCount() async {
    clearScreen();
    currentBin = null;
    if (currentCountItem == null) {
      if (allowRandom) {
        currentCountItem = await DBHelper().getNextItem(countInfo!, 0);
        currentCountItem = await DBHelper()
            .getPrevItem(countInfo!, currentCountItem!.count_id!);
      } else {
        currentCountItem = await DBHelper()
            .getPrevItem(countInfo!, await DBHelper().getNextCountId());
      }
    } else {
      currentCountItem =
          await DBHelper().getPrevItem(countInfo!, currentCountItem!.count_id!);
    }
    await setScreenValues();
    countIndicatorVisible = true;
    int totalCount = await DBHelper().getTotalFinishedCount(countInfo!);
    int currentCount = 0;
    if (currentCountItem == null) {
      currentCount = totalCount + 1;
    } else {
      currentCount = await DBHelper()
          .getCountPosition(countInfo!, currentCountItem!.count_id!);
    }
    String result = "";
    result += currentCount.toString();
    result += "/";
    result += totalCount.toString();
    countIndicator = result;
  }

  void clearScreen() {
    skuController.text = "";
    materialDesc = "";
    expiryDateController.text = "";
    batchController.text = "";
    majorUomController.text = "";
    minorUomController.text = "";
    setState(() {});
  }

  void _onNextButton() async {
    if (await DBHelper().getCountOfStockCount(
            countInfo!.area, countInfo!.section, countInfo!.direction) >
        0) {
      loadNextCount();
    }
  }

  void loadNextCount() async {
    clearScreen();
    currentBin = null;
    if (currentCountItem == null) {
      if (allowRandom) {
        currentCountItem = await DBHelper().getNextItem(countInfo!, 0);
      } else {
        currentCountItem = await DBHelper()
            .getNextItem(countInfo!, await DBHelper().getNextCountId());
      }
    } else {
      currentCountItem =
          await DBHelper().getNextItem(countInfo!, currentCountItem!.count_id!);
    }
    await setScreenValues();
    countIndicatorVisible = true;
    int totalCount = await DBHelper().getTotalFinishedCount(countInfo!);
    int currentCount = 0;
    if (currentCountItem == null) {
      currentCount = totalCount + 1;
    } else {
      currentCount = await DBHelper()
          .getCountPosition(countInfo!, currentCountItem!.count_id!);
    }
    String result = "";
    result += currentCount.toString();
    result += "/";
    result += totalCount.toString();
    countIndicator = result;
  }

  Future<void> saveCount() async {
    try {
      STOCK_COUNT_ITEM? items = await DBHelper().getCountBasedOnBin(
          countInfo!.area, countInfo!.section, countInfo!.direction, bin,
          countId: currentCountItem != null ? currentCountItem!.count_id : 0);
      if (items != null) {
        items.count_date = Utils.getCurrentDate();
        items.mat_no = skuController.text;
        items.status = getCountStatus();
        if (skuController.text.isNotEmpty) {
          items.mat_no = skuController.text;
        }
        if (editBatch == false && batchController.text.isNotEmpty) {
          items.batch_no = batchController.text;
        }
        if (editBatch == true && batchController.text.isEmpty) {
          items.batch_no = "";
        }
        if (editExp == false && expiryDateController.text.isNotEmpty) {
          items.expiry_dt = expiryDateController.text;
        }
        if (editExp == true && expiryDateController.text.isEmpty) {
          items.expiry_dt = "";
        }
        if (majorUomController.text.isNotEmpty) {
          items.major_unit_qty = double.parse(majorUomController.text);
        }
        if (minorUomController.text.isNotEmpty) {
          items.minor_unit_qty = double.parse(minorUomController.text);
        }
        items.split_bin = recordIsSplitBin ? "1" : "0";
        countID = items.count_id!;
        currentCountItem = items;
        await DBHelper().updateCountItem(currentCountItem!);
      } else {
        STOCK_COUNT_ITEM countItem = STOCK_COUNT_ITEM(count_id: 0);
        STOCK_COUNT_HEADER? countHeader = await DBHelper().getCountHeader();
        countItem.lid = FrameworkHelper.getUUID();
        countItem.fid = countHeader!.lid;
        countItem.count_id = await DBHelper().getNextCountId();
        countItem.count_date = Utils.getCurrentDate(); //have to set
        countItem.direction = countInfo!.direction;
        countItem.area = countInfo!.area;
        countItem.areasection = countInfo!.section;
        countItem.bin = bin;
        countItem.mat_no = skuController.text;
        countItem.pallet = "";
        countItem.dombbd = "";
        countItem.status = getCountStatus();
        if (skuController.text.isNotEmpty) {
          countItem.mat_no = skuController.text;
        }
        if (editBatch == false && batchController.text.isNotEmpty) {
          countItem.batch_no = batchController.text;
        }
        if (editBatch == true && batchController.text.isEmpty) {
          countItem.batch_no = "";
        }
        if (editExp == false && expiryDateController.text.isNotEmpty) {
          countItem.expiry_dt = expiryDateController.text;
        }
        if (editExp == true && expiryDateController.text.isEmpty) {
          countItem.expiry_dt = "";
        }
        if (majorUomController.text.isNotEmpty) {
          countItem.major_unit_qty = double.parse(majorUomController.text);
        }
        if (minorUomController.text.isNotEmpty) {
          countItem.minor_unit_qty = double.parse(minorUomController.text);
        }
        countItem.split_bin = recordIsSplitBin ? "1" : "0";
        countID = countItem.count_id!;
        currentCountItem = countItem;
        await DBHelper().insertCountItem(currentCountItem!);
      }
    } catch (e) {
      Logger.logError("CountPage", "saveCount", e.toString());
    }
  }

  void _onSplitButton() {
    splitBin();
  }

  void splitBin() async {
    if (isSaveButtonEnable) {
      if (!isValidToSave()) {
        return;
      }
      await saveCount();
    }
    clearScreen();
    String? currentBin = bin;
    String newBin = "";
    if (currentBin.isEmpty) {
      currentBin = await DBHelper().getMaxBinLetter(countInfo!);
      if (currentBin.isEmpty) {
        newBin = "a";
      } else {
        String newChar = currentBin.substring(currentBin.length - 1)[0];
        if (newChar == String.fromCharCode(startOfSplittingLetter!)) {
          startOfSplittingLetter = (startOfSplittingLetter! + 1);
          newChar = String.fromCharCode(startOfSplittingLetter!);
        }
        newBin = currentBin.substring(0, currentBin.length - 1) + newChar;
      }
    } else {
      if (recordIsSplitBin) {
        currentBin = currentBin.substring(0, currentBin.length - 1);
      }
      String? currentSubBin =
          await DBHelper().getMaxSubBinLetter(countInfo!, currentBin);
      if (currentSubBin.isEmpty || currentSubBin == currentBin) {
        newBin = currentBin + "a";
      } else {
        String newChar = currentSubBin.substring(currentSubBin.length - 1)[0];
        if (newChar == String.fromCharCode(startOfSplittingLetter!)) {
          startOfSplittingLetter = (startOfSplittingLetter! + 1);
          newChar = String.fromCharCode(startOfSplittingLetter!);
        }
        newBin = currentSubBin.substring(0, currentSubBin.length - 1) + newChar;
      }
    }
    recordIsSplitBin = true;
    bin = newBin;
    isSaveButtonEnable = true;
    //if (formKey.currentState!.validate()) {
    FocusScope.of(context).requestFocus(skuFocus);
    onTapMajDisable = true;
    onTapSingleMajorDisable = true;
    onTapSingleMinorDisable = true;
    int totalCount = await DBHelper().getTotalFinishedCount(countInfo!);
    if (totalCount > 0) {
      countIndicatorVisible = true;
      int currentCount = 0;
      if (currentCountItem == null) {
        currentCount = totalCount + 1;
      } else {
        currentCount = await DBHelper()
            .getCountPosition(countInfo!, currentCountItem!.count_id!);
      }
      String result = "";
      result += currentCount.toString();
      result += "/";
      result += totalCount.toString();
      countIndicator = result;
    }
    //}
  }

  String? validateSku(String? value) {
    // if (value!.isEmpty) {
    //   FocusScope.of(context).requestFocus(skuFocus);
    //   return AppLocalizations.of(context)!.enterOrScanMaterial;
    // }
    if (skuController.text.isEmpty) {
      FocusScope.of(context).requestFocus(skuFocus);
      return AppLocalizations.of(context)!.enterOrScanMaterial;
    } else {
      return null;
    }
  }

  String? validateBatch(String? value) {
    if (!editBatch && batchController.text.isEmpty && !batchCheck) {
      FocusScope.of(context).requestFocus(batchFocus);
      return "Enter batch Value";
    } else {
      return null;
    }
  }

  String? validateDate(String? value) {
    if (!editExp && !expCheck) {
      if (expiryDateController.text.isEmpty) {
        // expiryDateTextLayout.setError("Enter Expired date");
        FocusScope.of(context).requestFocus(expFocus);
        return "Enter Expired date";
      }
      if (!Utils.isValidExpireDateFormat(expiryDateController.text)) {
        // expiryDateError = "Invalid date (ddmmyyyy)";
        FocusScope.of(context).requestFocus(expFocus);
        return "Invalid date (ddmmyyyy)";
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  String? validateMOJ(String? value) {
    // if (value!.isEmpty) {
    //   FocusScope.of(context).requestFocus(majorFocus);
    //   return AppLocalizations.of(context)!.enterValue;
    // }

    if (majorUomController.text.isEmpty) {
      return AppLocalizations.of(context)!.enterValue;
    } else {
      return null;
    }
  }

  String? validateMIN(String? value) {
    // if (value!.isEmpty) {
    //   FocusScope.of(context).requestFocus(minorFocus);
    //   return AppLocalizations.of(context)!.enterValue;
    // }
    if (minorUomController.text.isEmpty) {
      return AppLocalizations.of(context)!.enterValue;
    } else {
      return null;
    }
  }

  bool isValidToSave() {
    // if (skuController.text.isEmpty) {
    //   if (!formSKU.currentState!.validate()) {
    //     FocusScope.of(context).requestFocus(skuFocus);
    //     return false;
    //   }
    // }
    if (skuController.text.isNotEmpty) {
      if (formSKU.currentState!.validate()) {}
    }
    if (!editBatch && batchController.text.isEmpty && !batchCheck) {
      //batchTextLayout.setError("Enter batch Value");
      if (!formBatch.currentState!.validate()) {
        FocusScope.of(context).requestFocus(batchFocus);
        return false;
      }
    } else {
      if (formBatch.currentState!.validate()) {}
    }
    if (!editExp && !expCheck) {
      if (expiryDateController.text.isEmpty) {
        if (!formDate.currentState!.validate()) {
          // expiryDateTextLayout.setError("Enter Expired date");
          FocusScope.of(context).requestFocus(expFocus);
          return false;
        }
      }
      if (!Utils.isValidExpireDateFormat(expiryDateController.text)) {
        if (!formDate.currentState!.validate()) {
          // expiryDateError = "Invalid date (ddmmyyyy)";
          FocusScope.of(context).requestFocus(expFocus);
          return false;
        }
      }
      if (formDate.currentState!.validate()) {}
    } else {
      if (formDate.currentState!.validate()) {}
    }

    if (majorUomController.text.isEmpty) {
      if (!formMoj.currentState!.validate()) {
        // majUomLabel.setError("Enter Value");
        return false;
      }
    }
    if (majorUomController.text.isNotEmpty) {
      if (formMoj.currentState!.validate()) {}
    }
    if (minorUomController.text.isEmpty) {
      if (!formMin.currentState!.validate()) {
        // minUomLabel.setError("Enter Value");
        return false;
      }
    }
    if (minorUomController.text.isNotEmpty) {
      if (formMin.currentState!.validate()) {}
    }

    return true;
  }

  String getCountStatus() {
    String status = radioButton;
    if ("Unrestricted" == status) {
      return "1";
    }
    if ("Quality" == status) {
      return "2";
    }
    if ("Blocked" == status) {
      return "4";
    }
    return "0";
  }

  void _onSaveButton() {
    if (!isValidToSave()) {
      return;
    }
    _onSaveCount();
  }

  void _onSaveCount() async {
    await saveCount();
    clearScreen();
    currentBin = null;
    if (await DBHelper().isCountFinished(countInfo!)) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return UnviredConfirmationDialog(
                title: AppLocalizations.of(context)!.info_count_finished,
                titleStyle: UIHelper().dialogTitleTextStyle(),
                positiveActionLabel: AppLocalizations.of(context)!.ok,
                onPositiveClickListener: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, PrepareForCountPage.routeName);
                });
          });
    } else {
      currentCountItem = await DBHelper().getNextItem(countInfo!, countID);
      await setScreenValues();
    }
    FocusScope.of(context).requestFocus(skuFocus);
    onTapMajDisable = true;
    onTapSingleMajorDisable = true;
    onTapSingleMinorDisable = true;
    int totalCount = await DBHelper().getTotalFinishedCount(countInfo!);
    if (totalCount > 0) {
      countIndicatorVisible = true;
      int currentCount = 0;
      if (currentCountItem == null) {
        currentCount = totalCount + 1;
      } else {
        currentCount = await DBHelper()
            .getCountPosition(countInfo!, currentCountItem!.count_id!);
      }
      String result = "";
      result += currentCount.toString();
      result += "/";
      result += totalCount.toString();
      countIndicator = result;
    }
  }

  void _onChangeSelectionButton() {
    Navigator.pushNamed(context, PrepareForCountPage.routeName);
  }

  void _onRecountButton() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return UnviredConfirmationDialog(
            title: AppLocalizations.of(context)!.info_re_count,
            titleStyle: UIHelper().dialogTitleTextStyle(),
            positiveActionLabel: AppLocalizations.of(context)!.no,
            negativeActionLabel: AppLocalizations.of(context)!.yes,
            onPositiveClickListener: () {
              Navigator.pop(context);
            },
            onNegativeClickListener: () async {
              await DBHelper().clearRecountStockCount(countInfo!.area,
                  countInfo!.section, countInfo!.direction, skuController.text);
              clearScreen();
              currentBin = null;
              currentCountItem = null;
              initScreenButtonsEnable();
              await setScreenValues();
              isSaveButtonEnable = true;
              Navigator.pop(context);
            },
          );
        });
  }
}

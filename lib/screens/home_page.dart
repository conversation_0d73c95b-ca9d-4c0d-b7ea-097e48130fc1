import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/logger.dart';

import 'package:nsts/be/INPUT_DOWNLOAD_COUNT_HEADER.dart';
import 'package:nsts/be/STOCK_COUNT_ITEM.dart';
import 'package:nsts/helper/ui_helper.dart';
import 'package:nsts/screens/prepare_for_count_page.dart';
import 'package:nsts/screens/stock_take_system_login_page.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:unvired_settings/main.dart';

import 'package:unvired_ui/unvired_ui.dart';
import 'package:unvired_ui/widgets/unvired_progress_dialog.dart';

import '../be/BREWERY_HEADER.dart';

import '../helper/db_helper.dart';
import '../helper/pa_helper.dart';
import '../utils/app_colors.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

import '../utils/constants.dart';
import '../widgets/ui_textstyle_helper.dart';
import 'count_page.dart';

class HomePage extends StatefulWidget {
  static const routeName = '/homePage';
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  static const sourceClass = 'HomePage';
  String appBar = '';
  String brewery_id = '';

  @override
  void initState() {
    setAppBar();
    super.initState();
    _initBreweryAreaSpinnerValues();
  }

  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColor.primaryDark,
          title: Text(appBar, style: TextStyle(color: AppColor.white)),
          automaticallyImplyLeading: false,
        ),
        body: Container(
          child: SingleChildScrollView(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.only(left: 32, right: 32),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        AppLocalizations.of(context)!.mainMenu,
                        style: TextStyle(
                            color: AppColor.primaryDark,
                            fontSize: 30,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Container(
                        child: UnviredElevatedButton(
                            containerWidth: 320,
                            buttonBackGroundColor:
                                MaterialStateProperty.all(AppColor.primaryDark),
                            buttonBorderRadius: BorderRadius.circular(16),
                            text: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '${AppLocalizations.of(context)!.newCount}',
                                style: UIHelper().buttonTextStyle(),
                              ),
                            ),
                            textPadding: EdgeInsets.all(0),
                            onPressCallBack: () {
                              _onNewCountButton();
                            }),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: UnviredElevatedButton(
                          containerWidth: 320,
                          buttonBackGroundColor:
                              MaterialStateProperty.all(AppColor.primaryDark),
                          text: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              '${AppLocalizations.of(context)!.continueCount}',
                              style: UIHelper().buttonTextStyle(),
                            ),
                          ),
                          textPadding: EdgeInsets.all(0),
                          onPressCallBack: () {
                            Navigator.pushNamed(
                                context, PrepareForCountPage.routeName);
                          }),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: UnviredElevatedButton(
                          containerWidth: 320,
                          buttonBackGroundColor:
                              MaterialStateProperty.all(AppColor.primaryDark),
                          text: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '${AppLocalizations.of(context)!.uploadCount}',
                                style: UIHelper().buttonTextStyle(),
                              )),
                          textPadding: EdgeInsets.all(0),
                          onPressCallBack: () {
                            _onUploadCountButton();
                          }),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: UnviredElevatedButton(
                          containerWidth: 320,
                          buttonBackGroundColor:
                              MaterialStateProperty.all(AppColor.primaryDark),
                          text: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '${AppLocalizations.of(context)!.download_count}',
                                style: UIHelper().buttonTextStyle(),
                              )),
                          textPadding: EdgeInsets.all(0),
                          onPressCallBack: () {
                            _onDownloadCountButton();
                          }),
                    ),
                    Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: UnviredElevatedButton(
                            containerWidth: 320,
                            text: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '${AppLocalizations.of(context)!.logOff}',
                                style: UIHelper().buttonTextStyle(),
                              ),
                            ),
                            buttonBackGroundColor:
                                MaterialStateProperty.all(AppColor.primaryDark),
                            textPadding: EdgeInsets.all(0),
                            onPressCallBack: () async {
                              clearCredentials();
                            })),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void clearCredentials() async {
    try {
      List<SystemCredential> credential =
          await SettingsHelper().getSystemCredentials();
      await SettingsHelper().clearSystemCredential(credential[0]);
      Navigator.pushNamed(context, StockTakeSystemLoginPage.routeName);
    } catch (e) {
      Logger.logError(sourceClass, 'clearCredentials', e.toString());
    }
  }

  void setAppBar() async {
    BREWERY_HEADER? header = await DBHelper().getBreweryHeader();
    if (header != null) {
      appBar = header.brewery_desc.toString();
      brewery_id = header.brewery_id.toString();
      setState(() {});
    }
  }

  void _initBreweryAreaSpinnerValues() async {
    breweryAreaSpinnerValues = await DBHelper().getAllAreas();
    breweryAreaSpinnerValues.insert(0, Constants.DEFAULT);
    dropdownBreweryValue = breweryAreaSpinnerValues.first;
  }

  Future<List<String>> _initAreaSelectionSpinnerValues(String area) async {
    List<String> allAreaSelection = [];
    List<String> allSections = await DBHelper().getAllSections(area);
    allSections.insert(0, Constants.DEFAULT);
    allAreaSelection = allSections;
    if (area == allAreaSelection.first) {
      return allAreaSelection = [];
    } else {
      return allAreaSelection;
    }
  }

  void _onNewCountButton() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return UnviredConfirmationDialog(
              title: AppLocalizations.of(context)!.info_clear_all_count,
              titleStyle: UIHelper().dialogTitleTextStyle(),
              positiveActionLabel: AppLocalizations.of(context)!.no,
              onPositiveClickListener: () => Navigator.pop(context),
              negativeActionLabel: AppLocalizations.of(context)!.yes,
              buttonTextStyle:
                  TextStyle(fontSize: 14, color: AppColor.primaryDark),
              onNegativeClickListener: () async {
                Navigator.pop(context);
                showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) {
                      return WillPopScope(
                          onWillPop: () async => false,
                          child: UnviredProgressDialog(
                            title:
                                Text(AppLocalizations.of(context)!.please_wait),
                            description: Text(AppLocalizations.of(context)!
                                .clearingExistingCounts),
                            progressColor: AppColor.blue,
                          ));
                    });
                await DBHelper().clearStockCounts();
                Navigator.pop(context);
                Navigator.pushNamed(context, PrepareForCountPage.routeName);
              });
        });
  }

  void _onUploadCountButton() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return UnviredConfirmationDialog(
              title: AppLocalizations.of(context)!.info_upload_count,
              titleStyle: UIHelper().dialogTitleTextStyle(),
              positiveActionLabel: AppLocalizations.of(context)!.cancel,
              onPositiveClickListener: () => Navigator.pop(context),
              negativeActionLabel: AppLocalizations.of(context)!.ok,
              onNegativeClickListener: () async {
                Navigator.pop(context);
                showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) {
                      return UnviredConfirmationDialog(
                          title: AppLocalizations.of(context)!
                              .info_connect_to_dock,
                          titleStyle: UIHelper().dialogTitleTextStyle(),
                          positiveActionLabel: AppLocalizations.of(context)!.ok,
                          onPositiveClickListener: () {
                            Navigator.pop(context);
                            uploadCount();
                          });
                    });
              });
        });
  }

  uploadCount() async {
    if (!(await URLService.isInternetConnected())) {
      if (mounted) {
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              return UnviredConfirmationDialog(
                  title: AppLocalizations.of(context)!.no_internet_connection,
                  titleStyle: UIHelper().dialogTitleTextStyle(),
                  positiveActionLabel: AppLocalizations.of(context)!.ok,
                  onPositiveClickListener: () {
                    Navigator.pop(context);
                  });
            });
      }
    } else {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return WillPopScope(
                onWillPop: () async => false,
                child: UnviredProgressDialog(
                  title: Text(AppLocalizations.of(context)!.please_wait),
                  description:
                      Text(AppLocalizations.of(context)!.uploading_count),
                  progressColor: AppColor.blue,
                ));
          });

      Result? result = await PAHelper.uploadCount(context);
      try {
        if (result == null || result.body['InfoMessage'] != null) {
          Navigator.pop(context);
          if (result!.body['InfoMessage'][0]['category'] == 'FAILURE') {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) {
                  return UnviredConfirmationDialog(
                      title: AppLocalizations.of(context)!.info,
                      description: Container(
                        child: Column(
                          children: [
                            Text(
                              result.body['InfoMessage'][0]['message'],
                              style: TextStyle(color: AppColor.grey),
                            )
                          ],
                        ),
                      ),
                      positiveActionLabel: AppLocalizations.of(context)!.ok,
                      onPositiveClickListener: () {
                        Navigator.pop(context);
                      });
                });
          } else {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) {
                  return UnviredConfirmationDialog(
                    title: AppLocalizations.of(context)!.info,
                    description: Container(
                      child: Column(
                        children: [
                          Text(
                            AppLocalizations.of(context)!.noResponseFromServer,
                            style: TextStyle(color: AppColor.grey),
                          )
                        ],
                      ),
                    ),
                    positiveActionLabel: AppLocalizations.of(context)!.ok,
                    onPositiveClickListener: () {
                      Navigator.pop(context);
                    },
                  );
                });
          }
        } else {
          Navigator.pop(context);
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) {
                return UnviredConfirmationDialog(
                  title:
                      AppLocalizations.of(context)!.info_upload_count_success,
                  titleStyle: UIHelper().dialogTitleTextStyle(),
                  positiveActionLabel: AppLocalizations.of(context)!.ok,
                  onPositiveClickListener: () {
                    Navigator.pop(context);
                  },
                );
              });
        }
      } catch (e) {
        Logger.logError(sourceClass, "downloadMasterData", e.toString());
      }
    }
  }

  void _onDownloadCountButton() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              content: Column(
                children: [
                  breweryField(setState),
                  // areaSelectionField(setState)
                ],
              ),
              actions: [
                TextButton(
                    onPressed: (dropdownBreweryValue == '' ||
                            dropdownBreweryValue ==
                                breweryAreaSpinnerValues.first)
                        ? () {}
                        : () {
                            onContinueButton(dropdownBreweryValue);
                          },
                    child: Text(
                      AppLocalizations.of(context)!
                          .continue_button
                          .toUpperCase(),
                      style: (dropdownBreweryValue == '' ||
                              dropdownBreweryValue ==
                                  breweryAreaSpinnerValues.first)
                          ? TextStyle(color: AppColor.grey_500)
                          : TextStyle(color: Theme.of(context).primaryColor),
                    )),
                TextButton(
                  onPressed: () {
                    setState(() {
                      dropdownBreweryValue = breweryAreaSpinnerValues.first;
                      areaSelectionValues = [];
                    });
                    Navigator.pop(context);
                  },
                  child: Text(AppLocalizations.of(context)!.cancel,
                      style: TextStyle(color: Theme.of(context).primaryColor)),
                ),
              ],
              scrollable: true,
              titleTextStyle: UIHelper().dialogTitleTextStyle(),
              backgroundColor: Colors.white,
              titlePadding: null,
              buttonPadding: null,
              elevation: 0,
              alignment: Alignment.center,
              actionsAlignment: MainAxisAlignment.end,
            );
          });
        });
  }

  String sequence = '';
  String dropdownBreweryValue = '';
  String dropdownAreaSelectionValue = '';
  List<String> breweryAreaSpinnerValues = [];
  List<String> areaSelectionValues = [];

  Widget breweryField(StateSetter setState) {
    return Column(children: [
      Padding(
        padding: const EdgeInsets.only(left: 12.0),
        child: Align(
            alignment: Alignment.topLeft,
            child: Text(
              AppLocalizations.of(context)!.area,
              style: UITextStyleHelper.textStyleMedium(context,
                  color: AppColor.grey),
            )),
      ),
      Row(
        children: [
          Expanded(
            child: Container(
              color: AppColor.white,
              child: DropdownButtonHideUnderline(
                child: ButtonTheme(
                  alignedDropdown: true,
                  child: DropdownButton(
                    value: dropdownBreweryValue,
                    items: breweryAreaSpinnerValues.map((String value) {
                      return DropdownMenuItem(
                        value: value,
                        child: Text(
                          value,
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) async {
                      setState(() {
                        dropdownBreweryValue = newValue!;
                      });
                      if (newValue == breweryAreaSpinnerValues.first) {
                        sequence = '';
                      }
                      areaSelectionValues =
                          await _initAreaSelectionSpinnerValues(
                              dropdownBreweryValue);
                      dropdownAreaSelectionValue = areaSelectionValues.first;
                    },
                    style: TextStyle(color: AppColor.blue, fontSize: 20),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    ]);
  }

  Widget areaSelectionField(StateSetter setState) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 12.0),
          child: Align(
              alignment: Alignment.topLeft,
              child: Text(
                AppLocalizations.of(context)!.areaSelection,
                style: UITextStyleHelper.textStyleMedium(context,
                    color: AppColor.grey),
              )),
        ),
        Row(
          children: [
            Expanded(
              child: Container(
                color: AppColor.white,
                child: DropdownButtonHideUnderline(
                  child: ButtonTheme(
                    alignedDropdown: true,
                    child: DropdownButton(
                      value: dropdownAreaSelectionValue,
                      style: TextStyle(color: AppColor.blue, fontSize: 20),
                      items: areaSelectionValues.map((String value) {
                        return DropdownMenuItem(
                          value: value,
                          child: Text(value.split("&").first),
                        );
                      }).toList(),
                      onChanged: (String? newValue) async {
                        dropdownAreaSelectionValue = newValue!;
                        if (newValue != areaSelectionValues.first) {
                          sequence = newValue.split("&")[1];
                        } else {
                          sequence = '';
                        }
                        setState(() {});
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void onContinueButton(String dropdownBreweryValuee) async {
    List<STOCK_COUNT_ITEM> items =
        await DBHelper().getCountItem(dropdownBreweryValuee);
    if (items.isNotEmpty) {
      Navigator.pop(context);
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return UnviredConfirmationDialog(
              title: AppLocalizations.of(context)!
                  .info_of_continue_count_for_download,
              titleStyle: UIHelper().dialogTitleTextStyle(),
              positiveActionLabel:
                  AppLocalizations.of(context)!.ignore_download_button,
              onPositiveClickListener: () {
                Navigator.pop(context);
                setState(() {
                  dropdownBreweryValue = breweryAreaSpinnerValues.first;
                  // areaSelectionValues = [];
                });
              },
              negativeActionLabel: AppLocalizations.of(context)!.proceed_button,
              onNegativeClickListener: () {
                onProceedButton(dropdownBreweryValuee);
              },
            );
          });
    } else {
      onProceedButton(dropdownBreweryValuee);
    }
  }

  Future<void> onProceedButton(String dropdownBreweryValuee) async {
    INPUT_DOWNLOAD_COUNT_HEADER input_download_count_header =
        INPUT_DOWNLOAD_COUNT_HEADER(
            brewery_id: brewery_id, area: dropdownBreweryValuee);
    await DBHelper().clearStockCountItem(dropdownBreweryValuee);
    // await DBHelper().clearStockCounts();
    // await DBHelper()
    //     .clearStockCountItem(dropdownBreweryValuee, breweryAreaSpinnerValue);
    Navigator.pop(context);
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return WillPopScope(
              onWillPop: () async => false,
              child: UnviredProgressDialog(
                title: Text(AppLocalizations.of(context)!.please_wait),
                description:
                    Text(AppLocalizations.of(context)!.downloading_count),
                progressColor: AppColor.blue,
              ));
        });
    setState(() {
      dropdownBreweryValue = breweryAreaSpinnerValues.first;
      // areaSelectionValues = [];
    });
    try {
      Result? result =
          await PAHelper.downloadCount(context, input_download_count_header);
      if (result == null || result.body['InfoMessage'] != null) {
        Navigator.pop(context);
        if (result!.body['InfoMessage'][0]['category'] == 'FAILURE') {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) {
                return UnviredConfirmationDialog(
                    title: AppLocalizations.of(context)!.info,
                    description: Container(
                      child: Column(
                        children: [
                          Text(
                            result.body['InfoMessage'][0]['message'],
                            style: TextStyle(color: AppColor.grey),
                          )
                        ],
                      ),
                    ),
                    positiveActionLabel: AppLocalizations.of(context)!.ok,
                    onPositiveClickListener: () {
                      Navigator.pop(context);
                    });
              });
        } else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) {
                return UnviredConfirmationDialog(
                  title: AppLocalizations.of(context)!.info,
                  description: Container(
                    child: Column(
                      children: [
                        Text(
                          AppLocalizations.of(context)!.noResponseFromServer,
                          style: TextStyle(color: AppColor.grey),
                        )
                      ],
                    ),
                  ),
                  positiveActionLabel: AppLocalizations.of(context)!.ok,
                  onPositiveClickListener: () {
                    Navigator.pop(context);
                  },
                );
              });
        }
      } else {
        Navigator.pop(context);
        // List<STOCK_COUNT_ITEM>? items =
        await DBHelper().getCountItems();
        //   for (STOCK_COUNT_ITEM data in items) {
        //     CountPageIntent countPageIntent = CountPageIntent(
        //         area: data.area!,
        //         section: data.areasection!,
        //         direction: data.direction!,
        //         sequence: data.bin!);
        //     Navigator.pushNamed(context, CountPage.routeName,
        //         arguments: countPageIntent);
        //   }
      }
    } catch (e) {
      Logger.logError(sourceClass, "onContinueButton", e.toString());
    }
  }
}

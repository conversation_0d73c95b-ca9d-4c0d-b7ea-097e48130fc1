import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/logger.dart';
import 'package:nsts/helper/pa_helper.dart';
import 'package:nsts/screens/home_page.dart';
import 'package:nsts/screens/splash_screen.dart';
import 'package:nsts/widgets/ui_textstyle_helper.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:unvired_settings/main.dart';
import 'package:unvired_ui/unvired_ui.dart';
import 'package:unvired_ui/widgets/unvired_progress_dialog.dart';

import '../helper/ui_helper.dart';
import '../utils/app_colors.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

class StockTakeSystemLoginPage extends StatefulWidget {
  static const routeName = '/stockTakeSystemLoginPage';

  const StockTakeSystemLoginPage({Key? key}) : super(key: key);

  @override
  State<StockTakeSystemLoginPage> createState() =>
      _StockTakeSystemLoginPageState();
}

class _StockTakeSystemLoginPageState extends State<StockTakeSystemLoginPage> {
  static const sourceClass = 'Stock Take System Login Page';
  double _windowHeight = 0.0;
  double _windowWidth = 0.0;
  GlobalKey<FormState> formkey = GlobalKey<FormState>();
  bool isLoginButtonActive = true;
  bool showPassword = true;
  TextEditingController loginIDController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController optionLoginPasswordController = TextEditingController();
  var appVersion = '';
  FocusNode loginIdFocusNode = FocusNode();
  FocusNode pwdFocusNode = FocusNode();

  @override
  void initState() {
    _initAppVersion();
    super.initState();
  }

  @override
  void dispose() {
    loginIDController.dispose();
    passwordController.dispose();
    optionLoginPasswordController.dispose();
    loginIdFocusNode.dispose();
    pwdFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _windowHeight = MediaQuery.of(context).size.height;
    _windowWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      body: SafeArea(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          child: Form(
            key: formkey,
            child: Container(
                height: double.infinity,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.all(8),
                        child: Text(
                          AppLocalizations.of(context)!.stockTakeSystem,
                          style: UITextStyleHelper.textStyleLarge(context,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(2),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                                '${AppLocalizations.of(context)!.version}R-${appVersion}',
                                style: UITextStyleHelper.textStyleMedium(
                                    context,
                                    color: AppColor.grey_600)),
                          ],
                        ),
                      ),
                      Padding(
                          padding: EdgeInsets.only(left: 8, right: 8, top: 8),
                          child: _getLoginIDTextField(context)),
                      Padding(
                          padding: EdgeInsets.only(left: 8, right: 8, top: 8),
                          child: _getPasswordTextField(context)),
                      Padding(
                        padding: EdgeInsets.only(left: 8, top: 20, right: 8),
                        child: _loginButton(context),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 8, top: 10, right: 8),
                        child: _exitButton(),
                      ),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Padding(
                          padding: EdgeInsets.all(8),
                          child: _optionButton(),
                        ),
                      ),
                    ],
                  ),
                )),
          ),
        ),
      ),
    );
  }

  Widget _getLoginIDTextField(BuildContext context) {
    return UnviredTextField(
      controller: loginIDController,
      readOnly: false,
      onTap: () {},
      focusNode: loginIdFocusNode,
      label: AppLocalizations.of(context)!.loginId,
      focusBorder:
          UnderlineInputBorder(borderSide: BorderSide(color: AppColor.blue)),
      filled: false,
      clearIconSuffix: false,
      onChange: (value) {
        TextSelection previousSelection = loginIDController.selection;
        loginIDController.text = value;
        loginIDController.selection = previousSelection;
        setState(() {});
      },
      errorBorder:
          UnderlineInputBorder(borderSide: BorderSide(color: AppColor.red)),
      validate: (val) {
        if (val!.isEmpty) {
          return AppLocalizations.of(context)!.enterLoginID;
        }
      },
    );
  }

  Widget _getPasswordTextField(BuildContext context) {
    return UnviredPasswordField(
      passwordController: passwordController,
      readOnly: false,
      onTap: () {},
      focusNode: pwdFocusNode,
      lable: AppLocalizations.of(context)!.password,
      focusBorder:
          UnderlineInputBorder(borderSide: BorderSide(color: AppColor.blue)),
      errorBorder:
          UnderlineInputBorder(borderSide: BorderSide(color: AppColor.red)),
      passwordEyeIconColor: passwordController.text.isEmpty
          ? AppColor.white
          : AppColor.primaryDark,
      filled: false,
      onChange: (value) {
        TextSelection previousSelection = passwordController.selection;
        passwordController.text = value;
        passwordController.selection = previousSelection;
        setState(() {});
      },
      validate: (val) {
        if (val!.isEmpty) {
          return AppLocalizations.of(context)!.enterPassword;
        }
      },
    );
  }

  Widget _loginButton(BuildContext context) {
    return Container(
      width: 600,
      height: 43,
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor:
              MaterialStateProperty.all(Theme.of(context).primaryColor),
          shape: MaterialStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.0),
            ),
          ),
        ),
        onPressed: () {
          FocusScope.of(context).unfocus();
          if (formkey.currentState!.validate()) {
            String user = loginIDController.text;
            String pswd = passwordController.text;
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) {
                  return WillPopScope(
                      onWillPop: () async => false,
                      child: UnviredProgressDialog(
                        title: Text(AppLocalizations.of(context)!.please_wait),
                        description: Text(
                            AppLocalizations.of(context)!.refreshingMasterData),
                        progressColor: AppColor.colorAccent,
                      ));
                });
            saveCredentials(user, pswd, context);
          }
        },
        child: Text(
          AppLocalizations.of(context)!.login,
          style: TextStyle(color: Colors.white, fontSize: 16),
        ),
      ),
    );
  }

  Widget _exitButton() {
    return Container(
      width: 80,
      height: 43,
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor:
              MaterialStateProperty.all(Theme.of(context).primaryColor),
          shape: MaterialStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.0),
            ),
          ),
        ),
        onPressed: () {
          exit(0);
        },
        child: Text(
          AppLocalizations.of(context)!.exit,
          style: TextStyle(color: Colors.white, fontSize: 16),
        ),
      ),
    );
  }

  Widget _optionButton() {
    return Container(
      height: 43,
      child: TextButton(
        onPressed: () {
          _onOptions();
        },
        child: Text(
          AppLocalizations.of(context)!.options,
          style:
              UITextStyleHelper.textStyleMedium(context, color: AppColor.blue),
        ),
      ),
    );
  }

  _initAppVersion() async {
    appVersion = await SettingsHelper().getApplicationVersionNumber();
    setState(() {});
  }

  void saveCredentials(
      final String user, final String pswd, BuildContext context) async {
    try {
      List<SystemCredential> credentials =
          await SettingsHelper().getSystemCredentials();
      await updateSystemLoginCredentials(credentials[0], user, pswd, context);
    } catch (e) {
      Logger.logError(sourceClass, "saveCredentials", e.toString());
    }
  }

  void _onOptions() {
    int countofPress = 0;
    showUnviredDialogWithInput(context,
        title: '',
        content: StatefulBuilder(builder: (context, StateSetter setState) {
          return UnviredPasswordField(
            passwordController: optionLoginPasswordController,
            onTap: () {},
            filled: false,
            enableBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: AppColor.grey)),
            focusBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: AppColor.blue)),
            readOnly: false,
            onChange: (text) {
              TextSelection previousSelection =
                  optionLoginPasswordController.selection;
              optionLoginPasswordController.text = text;
              optionLoginPasswordController.selection = previousSelection;
              setState(() {});
            },
            passwordEyeIconColor: optionLoginPasswordController.text.isNotEmpty
                ? AppColor.blue
                : AppColor.white,
            hint: AppLocalizations.of(context)!.password,
          );
        }),
        positiveActionLabel: AppLocalizations.of(context)!.ok,
        onPositiveClickListener: () {
          if (optionLoginPasswordController.text.isEmpty ||
              optionLoginPasswordController.text != "fyeo") {
            optionLoginPasswordController.clear();
            Navigator.pop(context);
          } else {
            optionLoginPasswordController.clear();
            Navigator.pop(context);
            showDialog(
                barrierDismissible: false,
                context: context,
                builder: (context) {
                  return UnviredDialog(
                    title: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: GestureDetector(
                        child: Center(
                            child: Text(
                          AppLocalizations.of(context)!.options,
                          style: TextStyle(
                              color: AppColor.black,
                              fontWeight: FontWeight.bold,
                              fontSize: 20),
                        )),
                        onTap: () {
                          if (countofPress++ == 7) {
                            countofPress = 0;
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) {
                                  return Settings(
                                    themeData: ThemeData(),
                                  );
                                },
                              ),
                            );
                          }
                        },
                      ),
                    ),
                    dialogBorderRadius: BorderRadius.circular(3),
                    content: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Container(
                        child: Column(
                          children: [
                            UnviredElevatedButton(
                              containerWidth: 320,
                              containerHeight: 48,
                              buttonBackGroundColor: MaterialStateProperty.all(
                                  AppColor.primaryDark),
                              text: Center(
                                child: Text(AppLocalizations.of(context)!
                                    .sendLogsToSerever),
                              ),
                              textPadding: EdgeInsets.all(0),
                              onPressCallBack: () {
                                sendLogsToServer();
                              },
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            UnviredElevatedButton(
                              containerWidth: 320,
                              containerHeight: 48,
                              buttonBackGroundColor: MaterialStateProperty.all(
                                  AppColor.primaryDark),
                              onPressCallBack: () {
                                Navigator.pop(context);
                                showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (context) {
                                      return UnviredConfirmationDialog(
                                          title: AppLocalizations.of(context)!
                                              .warning,
                                          description: Text(
                                              AppLocalizations.of(context)!
                                                  .clearDataWarning),
                                          positiveActionLabel:
                                              AppLocalizations.of(context)!.ok,
                                          onPositiveClickListener: () async {
                                            await SettingsHelper().clearData();
                                            Navigator.pop(context);
                                            Navigator.pushAndRemoveUntil(
                                                context, MaterialPageRoute(
                                                    builder: (context) {
                                              return SplashScreen();
                                            }), (route) => false);
                                          },
                                          negativeActionLabel:
                                              AppLocalizations.of(context)!
                                                  .cancel,
                                          onNegativeClickListener: () =>
                                              Navigator.pop(context));
                                    });
                              },
                              text: Center(
                                child: Text(AppLocalizations.of(context)!
                                    .clearAllApplicationData),
                              ),
                              textPadding: EdgeInsets.all(0),
                            ),
                          ],
                        ),
                      ),
                    ),
                    actions: [],
                  );
                });
          }
        },
        actionLabelStyle: TextStyle(color: AppColor.primaryDark, fontSize: 14));
  }

  Future<void> updateSystemLoginCredentials(credential, String username,
      String password, BuildContext contextt) async {
    try {
      await SettingsHelper()
          .updateSystemCredential(credential, username, password);
    } catch (e) {
      Logger.logError(sourceClass, 'updateSystemCredentials', e.toString());
      if (e.toString().isNotEmpty) {
        Navigator.pop(context);
        await SettingsHelper().clearSystemCredential(credential);
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              return WillPopScope(
                onWillPop: () async {
                  return false;
                },
                child: UnviredConfirmationDialog(
                    title: AppLocalizations.of(context)!.error,
                    description: Container(
                      child: Column(
                        children: [Text(e.toString())],
                      ),
                    ),
                    positiveActionLabel: AppLocalizations.of(context)!.ok,
                    onPositiveClickListener: () async {
                      Navigator.pop(context);
                      loginIDController.clear();
                      passwordController.clear();
                      FocusScope.of(contextt).requestFocus(loginIdFocusNode);
                    }),
              );
            });
      } else {
        if (!(await URLService.isInternetConnected())) {
          Navigator.pop(context);
          if (mounted) {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) {
                  return UnviredConfirmationDialog(
                      title:
                          AppLocalizations.of(context)!.no_internet_connection,
                      titleStyle: UIHelper().dialogTitleTextStyle(),
                      positiveActionLabel: AppLocalizations.of(context)!.ok,
                      onPositiveClickListener: () {
                        Navigator.pop(context);
                      });
                });
          }
        } else {
          downloadMasterData();
        }
      }
    }
  }

  void downloadMasterData() async {
    Result? result = await PAHelper.downloadMasterDataMode(context);
    try {
      if (result == null || result.body['InfoMessage'] != null) {
        if (result!.body['InfoMessage'][0]['category'] == 'FAILURE') {
          Navigator.pop(context);
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) {
                return UnviredConfirmationDialog(
                    title: AppLocalizations.of(context)!.error,
                    description: Container(
                      child: Column(
                        children: [
                          Text(result.body['InfoMessage'][0]['message'])
                        ],
                      ),
                    ),
                    positiveActionLabel: AppLocalizations.of(context)!.ok,
                    onPositiveClickListener: () async {
                      Navigator.pop(context);
                    });
              });
        } else {
          Navigator.pop(context);
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) {
                return UnviredConfirmationDialog(
                    title: AppLocalizations.of(context)!.error,
                    description: Container(
                      child: Column(
                        children: [
                          Text(AppLocalizations.of(context)!
                              .noResponseFromServer)
                        ],
                      ),
                    ),
                    positiveActionLabel: AppLocalizations.of(context)!.ok,
                    onPositiveClickListener: () async {
                      Navigator.pop(context);
                    });
              });
        }
      } else {
        Navigator.pop(context);
        loginIDController.clear();
        passwordController.clear();
        Navigator.pushNamed(context, HomePage.routeName);
      }
    } catch (e) {
      Logger.logError(sourceClass, "downloadMasterData", e.toString());
    }
  }

  sendLogsToServer() async {
    try {
      await SettingsHelper().sendLogsToServer();
      Fluttertoast.showToast(
          msg: AppLocalizations.of(context)!.logsSentToServer,
          backgroundColor: AppColor.white,
          textColor: AppColor.black);
    } catch (e) {
      Logger.logError(sourceClass, "sendLogsToServer", e.toString());
    }
  }
}

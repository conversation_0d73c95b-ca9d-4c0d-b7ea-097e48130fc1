import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:unvired_ui/unvired_ui.dart';

import '../models/app_url.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';
import '../widgets/server_option_dialog.dart';

class LoginPageIntent {
  final bool selectFromMultipleAccounts;
  List<UnviredAccount>? accountList;
  UnviredAccount? selectedAccount;
  final String error;

  LoginPageIntent(
      {required this.selectFromMultipleAccounts,
      this.accountList,
      this.selectedAccount,
      required this.error});
}

class LoginPage extends StatefulWidget {
  static const routeName = "/loginPage";
  final LoginPageIntent loginPageIntent;

  LoginPage({Key? key, required this.loginPageIntent}) : super(key: key);

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  GlobalKey<FormState> formkey = GlobalKey<FormState>();
  double _windowHeight = 0.0;
  double _windowWidth = 0.0;
  List<AppUrl> urlList = [];
  bool isLoginButtonActive = false;
  bool showPassword = true;
  AppUrl? selectedAppUrl;
  TextEditingController usernameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController urlController = TextEditingController();
  String applicationVersion = '';
  FocusNode _urlCursorFocus = FocusNode();
  FocusNode _usernameCursorFocus = FocusNode();
  FocusNode _passwordCursorFocus = FocusNode();

  @override
  void initState() {
    if (widget.loginPageIntent.selectedAccount != null) {
      urlController.text = widget.loginPageIntent.selectedAccount!.getUrl();
    } else {
      urlController.text = 'https://ump.za.ab-inbev.com/UMP';
    }
    _initUrls();
    _getAppVersion();
    if (widget.loginPageIntent.error.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              return UnviredConfirmationDialog(
                title: AppLocalizations.of(context)!.error,
                description: Text(widget.loginPageIntent.error),
                positiveActionLabel: AppLocalizations.of(context)!.ok,
                onPositiveClickListener: () {
                  Navigator.pop(context);
                },
                buttonTextStyle: TextStyle(color: AppColor.green),
              );
            });
        if (widget.loginPageIntent.selectedAccount != null) {
          usernameController.text =
              widget.loginPageIntent.selectedAccount!.getUserName();
        }
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _windowHeight = MediaQuery.of(context).size.height;
    _windowWidth = MediaQuery.of(context).size.width;
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
        statusBarColor: AppColor.primaryDark,
        systemNavigationBarColor: AppColor.primaryDark));
    return WillPopScope(
        onWillPop: () async {
          exit(0);
        },
        child: Scaffold(
          body: SafeArea(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              child: Container(
                  height: double.infinity,
                  decoration: BoxDecoration(
                    color: AppColor.primaryDark,
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        _getCompanyLogo(),
                        _getLoginCard(context),
                        _getVersionCard(context)
                      ],
                    ),
                  )),
            ),
          ),
        ));
  }

  Widget _getCompanyLogo() {
    return Image.asset(
      'assets/images/login_screen_image.png',
      height: _windowHeight * 0.23,
      width: _windowWidth * 0.63,
    );
  }

  Widget _getLoginCard(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6.0),
      ),
      child: Form(
        key: formkey,
        child: Container(
          padding: EdgeInsets.only(left: 10, right: 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _getUnviredLoginHeader(),
              _getUrlTextField(context),
              _getUserTextField(context),
              _getPasswordField(context),
              _getLoginButton()
            ],
          ),
        ),
      ),
    );
  }

  Widget _getVersionCard(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 18),
      child: Card(
        margin: EdgeInsets.symmetric(horizontal: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6.0),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                child: Row(children: [
                  Text(
                    '${AppLocalizations.of(context)!.version} R-${applicationVersion}',
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black54),
                  )
                ])),
          ],
        ),
      ),
    );
  }

  Widget _getUnviredLoginHeader() {
    return Padding(
      padding: const EdgeInsets.only(left: 80, right: 80, top: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(child: Image.asset('assets/images/unvired_icon_32.png')),
          Text(
            AppLocalizations.of(context)!.unviredLogin,
            style: TextStyle(
              color: AppColor.grey_600,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _getUrlTextField(BuildContext context) {
    return Theme(
      data: ThemeData(
          textSelectionTheme: TextSelectionThemeData(
              selectionColor: Color(0xff348777),
              selectionHandleColor: Color(0xff348777))),
      child: UnviredTextField(
          controller: urlController,
          onTap: () {},
          readOnly: false,
          filled: false,
          label: AppLocalizations.of(context)!.url,
          labelTextStyle: _urlCursorFocus.hasFocus
              ? TextStyle(color: AppColor.blue)
              : TextStyle(color: AppColor.grey_600),
          focusBorder: UnderlineInputBorder(
              borderSide: BorderSide(
            color: Color(0xff348777),
            width: 1.4,
          )),
          errorBorder:
              UnderlineInputBorder(borderSide: BorderSide(color: AppColor.red)),
          focusedErrorBorder:
              UnderlineInputBorder(borderSide: BorderSide(color: AppColor.red)),
          errorStyle: TextStyle(color: AppColor.red),
          cursorColor: Color(0xff348777),
          focusNode: _urlCursorFocus,
          onChange: (v) {
            TextSelection previousSelection = urlController.selection;
            urlController.text = v;
            urlController.selection = previousSelection;
          },
          onSubmit: (val) {
            FocusScope.of(context).requestFocus(_usernameCursorFocus);
          },
          suffixIcon: IconButton(
              icon: Icon(
                Icons.arrow_drop_down_outlined,
                color: AppColor.primaryDark,
                size: 36,
              ),
              onPressed: () {
                if (urlList.isNotEmpty && urlList.length > 1) {
                  showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return ServerUrlOptionDialog(
                          title: AppLocalizations.of(context)!.pickURL,
                          selectedUrl: selectedAppUrl,
                          serverList: urlList,
                          urlController: urlController,
                          text: AppLocalizations.of(context)!.yes,
                          onUrlChanged: (AppUrl appUrl) {
                            selectedAppUrl = appUrl;
                          },
                        );
                      });
                }
              }),
          validate: (val) {
            if (val!.isEmpty) {
              return AppLocalizations.of(context)!.enterValidUrl;
            }
          }),
    );
  }

  Widget _getUserTextField(BuildContext context) {
    return Theme(
      data: ThemeData(
          textSelectionTheme: TextSelectionThemeData(
              selectionColor: Color(0xff348777),
              selectionHandleColor: Color(0xff348777))),
      child: UnviredTextField(
          controller: usernameController,
          readOnly: false,
          filled: false,
          onTap: () {},
          onChange: (v) {
            TextSelection previousSelection = usernameController.selection;
            usernameController.text = v;
            usernameController.selection = previousSelection;
            setState(() {});
          },
          label: AppLocalizations.of(context)!.userName,
          labelTextStyle: _usernameCursorFocus.hasFocus
              ? TextStyle(color: AppColor.blue)
              : TextStyle(color: AppColor.grey_600),
          focusBorder: UnderlineInputBorder(
              borderSide: BorderSide(
            color: Color(0xff348777),
            width: 1.4,
          )),
          errorBorder: urlController.text.isNotEmpty
              ? UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColor.red))
              : UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColor.grey)),
          focusedErrorBorder: urlController.text.isNotEmpty
              ? UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColor.red))
              : UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColor.grey)),
          errorStyle: TextStyle(color: AppColor.red),
          cursorColor: Color(0xff348777),
          focusNode: _usernameCursorFocus,
          onSubmit: (val) {
            FocusScope.of(context).requestFocus(_passwordCursorFocus);
          },
          clearIconSuffix: true,
          clearIconSuffixColor: AppColor.primaryDark,
          validate: (val) {
            if (val!.isEmpty && urlController.text.isNotEmpty) {
              return AppLocalizations.of(context)!.enterValidUsername;
            }
          }),
    );
  }

  Widget _getPasswordField(BuildContext context) {
    return Theme(
      data: ThemeData(
          textSelectionTheme: TextSelectionThemeData(
              selectionColor: Color(0xff348777),
              selectionHandleColor: Color(0xff348777))),
      child: UnviredPasswordField(
        lable: AppLocalizations.of(context)!.password,
        labelTextStyle: _passwordCursorFocus.hasFocus
            ? TextStyle(color: AppColor.blue)
            : TextStyle(color: AppColor.grey_600),
        passwordController: passwordController,
        focusNode: _passwordCursorFocus,
        onTap: () {},
        onChange: (v) {
          TextSelection previousSelection = passwordController.selection;
          passwordController.text = v;
          passwordController.selection = previousSelection;
          setState(() {});
        },
        focusBorder: UnderlineInputBorder(
            borderSide: BorderSide(
          color: Color(0xff348777),
          width: 1.4,
        )),
        errorBorder: usernameController.text.isNotEmpty
            ? UnderlineInputBorder(borderSide: BorderSide(color: AppColor.red))
            : UnderlineInputBorder(
                borderSide: BorderSide(color: AppColor.grey)),
        focusedErrorBorder: usernameController.text.isNotEmpty
            ? UnderlineInputBorder(borderSide: BorderSide(color: AppColor.red))
            : UnderlineInputBorder(
                borderSide: BorderSide(color: AppColor.grey)),
        errorStyle: TextStyle(color: AppColor.red),
        cursorColor: Color(0xff348777),
        passwordEyeIconColor: passwordController.text.isNotEmpty
            ? AppColor.primaryDark
            : AppColor.white,
        readOnly: false,
        filled: false,
        validate: (val) {
          if (val!.isEmpty && usernameController.text.isNotEmpty) {
            return AppLocalizations.of(context)!.enterValidPassword;
          }
        },
      ),
    );
  }

  Widget _getLoginButton() {
    return Padding(
      padding: EdgeInsets.only(top: 18, bottom: 20),
      child: Container(
        width: 239,
        height: 48,
        child: ElevatedButton(
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all(AppColor.primaryDark),
            shape: MaterialStateProperty.all(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
              ),
            ),
          ),
          onPressed: () {
            if (formkey.currentState!.validate()) {
              if (urlController.text.isNotEmpty &&
                  usernameController.text.isNotEmpty &&
                  passwordController.text.isNotEmpty) {
                setState(() {
                  isLoginButtonActive = true;
                });
                isLoginButtonActive ? _loginValidateData() : () {};
              }
            }
          },
          child: Text(
            AppLocalizations.of(context)!.login,
            style: TextStyle(
                color: AppColor.white,
                fontWeight: FontWeight.bold,
                fontSize: 16),
          ),
        ),
      ),
    );
  }

  _getAppVersion() async {
    applicationVersion = await SettingsHelper().getApplicationVersionNumber();
    setState(() {});
  }

  void _initUrls() async {
    await getURLs();
  }

  Future<List<AppUrl>> getURLs() async {
    urlList = [];
    final contents = await rootBundle.loadString(
      'assets/json/app_config.json',
    );
    final Map<String, dynamic> config = jsonDecode(contents);
    if (config.containsKey('urls')) {
      urlList = List<dynamic>.from(config['urls'])
          .map((e) => AppUrl.fromMap(e))
          .toList();
    }

    selectedAppUrl = urlList.firstWhere((element) => element.isDefault);
    return urlList;
  }

  void _loginValidateData() {
    if (urlController.text.isEmpty) {
      FocusScope.of(context).requestFocus(_urlCursorFocus);
    }
    if (usernameController.text.isEmpty) {
      FocusScope.of(context).requestFocus(_usernameCursorFocus);
    } else if (passwordController.text.isEmpty) {
      FocusScope.of(context).requestFocus(_passwordCursorFocus);
    } else {
      setState(() {
        isLoginButtonActive = true;
      });
      if (widget.loginPageIntent.selectedAccount != null) {
        widget.loginPageIntent.selectedAccount!
            .setUserName(usernameController.text.toString());
        widget.loginPageIntent.selectedAccount!
            .setPassword(passwordController.text.toString());
        widget.loginPageIntent.selectedAccount!
            .setCompany(AppConstants.APP_NAME);
        widget.loginPageIntent.selectedAccount!
            .setUrl(urlController.text.toString());
        widget.loginPageIntent.selectedAccount!.setLoginType(LoginType.unvired);
      } else {
        setState(() {
          isLoginButtonActive = true;
        });
        widget.loginPageIntent.selectedAccount = UnviredAccount()
          ..setUserName(usernameController.text.toString())
          ..setPassword(passwordController.text.toString())
          ..setCompany(AppConstants.APP_NAME)
          ..setUrl(urlController.text.toString())
          ..setLoginType(LoginType.unvired);
      }
      Navigator.pop(context, widget.loginPageIntent.selectedAccount);
    }
  }
}

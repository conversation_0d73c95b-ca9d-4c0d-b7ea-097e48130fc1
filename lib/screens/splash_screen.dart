import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:nsts/screens/stock_take_system_login_page.dart';
import 'package:nsts/utils/app_colors.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:permission_handler/permission_handler.dart';

import 'login_page.dart';

class SplashScreen extends StatefulWidget {
  static const routeName = "/";

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> implements AuthProtocol {
  double _windowHeight = 0.0;
  double _windowWidth = 0.0;

  @override
  void initState() {
    super.initState();
    _requestPermissions();
  }

  void _requestPermissions() async {
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    final int androidVersion = androidInfo.version.sdkInt;

    List<Permission> permissions = [
      Permission.location,
    ];

    if (androidVersion <= 33) {
      permissions.add(Permission.storage);
    } else {
      permissions.add(Permission.photos);
      permissions.add(Permission.mediaLibrary);
    }
    Map<Permission, PermissionStatus> statuses = await permissions.request();

    bool allPermissionsGranted = statuses.values.every((status) => status.isGranted);

    if (allPermissionsGranted) {
      checkLoginStatus();
    } else {
      exit(0);
    }
  }

  @override
  Future<UnviredAccount> showLoginScreen(List<UnviredAccount> accounts) async {
    UnviredAccount? selectedAccount;
    if (accounts.isEmpty) {
      selectedAccount = await _navigateToLoginPage(errorMessage: '', isMultipleAccounts: false, accountList: accounts);
    } else if (accounts.length == 1) {
      selectedAccount = accounts[0];
      if (selectedAccount.getIsLocalPasswordRequired() && selectedAccount.getIsLastLoggedIn()) {
        selectedAccount = await _navigateToLoginPage(
            errorMessage: AppLocalizations.of(context)!.pleaseEnterYourPasswordToLogin, isMultipleAccounts: false, accountList: accounts);
      } else if (selectedAccount.getErrorMessage().toString().isNotEmpty) {
        selectedAccount =
            await _navigateToLoginPage(errorMessage: selectedAccount.getErrorMessage().toString(), isMultipleAccounts: false, accountList: accounts);
      } else {
        selectedAccount = accounts[0];
      }
    } else {
      selectedAccount = await _navigateToLoginPage(
          errorMessage: AppLocalizations.of(context)!.pleaseSelectAccountToContinue, isMultipleAccounts: true, accountList: accounts);
    }
    return selectedAccount;
  }

  @override
  Future<SSOResult> showWebView(SSOLoginScreen ssoLoginScreen) async {
    // TODO: implement showWebView
    SSOResult value = await Navigator.push(context, CupertinoPageRoute(builder: (context) => ssoLoginScreen));
    return value;
  }

  @override
  Widget build(BuildContext context) {
    _windowHeight = MediaQuery.of(context).size.height;
    _windowWidth = MediaQuery.of(context).size.width;
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(statusBarColor: AppColor.primaryDark, systemNavigationBarColor: AppColor.primaryDark));
    return Scaffold(
      body: Container(
        height: double.infinity,
        width: double.infinity,
        color: AppColor.primaryDark,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _getCompanyLogo(),
            CircularProgressIndicator(
              backgroundColor: Colors.white,
              color: Colors.grey,
            ),
            Text(
              AppLocalizations.of(context)!.pleaseWaitThreeDots,
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  void checkLoginStatus() async {
    String metaDataJson = await DefaultAssetBundle.of(context).loadString("assets/json/metadata.json");
    bool isLoginSuccess = await (AuthenticationService()
          ..setAuthProtocol(this)
          ..setMetadataJSON(metaDataJson)
          ..setContext(context))
        .login();
    if (isLoginSuccess) {
      _navigateToStockTakeSystemLoginPage();
    }
  }

  Future<UnviredAccount> _navigateToLoginPage(
      {required String errorMessage, required bool isMultipleAccounts, required List<UnviredAccount> accountList}) async {
    LoginPageIntent loginPageIntent = LoginPageIntent(
        selectFromMultipleAccounts: isMultipleAccounts,
        error: errorMessage,
        accountList: accountList,
        selectedAccount: errorMessage.isNotEmpty ? accountList[0] : null);
    UnviredAccount unviredAccount = await Navigator.pushNamed(context, LoginPage.routeName, arguments: loginPageIntent) as UnviredAccount;
    return unviredAccount;
  }

  _navigateToStockTakeSystemLoginPage() {
    SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(statusBarColor: Theme.of(context).primaryColor, systemNavigationBarColor: Theme.of(context).primaryColor));
    Future.delayed(Duration(seconds: 1), () {
      Navigator.pushReplacementNamed(context, StockTakeSystemLoginPage.routeName);
    });
  }

  Widget _getCompanyLogo() {
    return Image.asset(
      'assets/images/app_logo.png',
      height: 200,
      width: _windowWidth * 0.75,
    );
  }
}

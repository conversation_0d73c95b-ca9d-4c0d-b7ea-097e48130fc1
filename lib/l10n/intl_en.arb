{"appName": "NSTS", "pleaseEnterYourPasswordToLogin": "Please Enter Your Password To Login", "pleaseSelectAccountToContinue": "Please Select Account To Continue", "pleaseWaitThreeDots": "Please Wait...", "unviredLogin": "<PERSON><PERSON><PERSON>", "yes": "YES", "url": "Url", "userName": "Username", "password": "Password", "login": "<PERSON><PERSON>", "app_name": "nSTS", "stockTakeSystem": "STOCK TAKE SYSTEM", "home_title": "SAB Newlands Brewery", "action_settings": "Settings", "ok": "OK", "cancel": "CANCEL", "no": "NO", "options": "Options", "reset_data": "Clear all application data", "reset_warn": "This option will clear all the data associated with the application and restore the application to the freshly installed state. Deleted data cannot be recovered. Are you sure want to clear the data in the application", "sendLogs": ">SEND LOGS TO SERVER", "confirm": "Confirm", "warning": "Warning", "info": "Information", "please_wait": "Please wait", "no_server_response": "No Response from Server", "info_masterdata_refresh": "Refreshing master data", "info_clear_all_count": "All existing counts will be cleared. Do you wish to continue?", "info_clearing_count": "Clearing existing counts&#8230;", "info_clear_for_new_count": "There is an existing count for this area and section. Do you wish to continue the count?", "info_clear_for_recount": "Do you want to clear all count information for area and section?", "info_select_area": "Please make a selection for Area.", "info_select_section": "Please make a selection for Section.", "info_re_count": "This will clear all counts for this material in this area and section. Do you want to re-count?", "info_decimal_value": "Decimals can only exist in the Minor UOM field. Can the decimals be placed in the Minor UOM field for this result?", "info_count_finished": "All bins have been counted. Returning to Main Menu.", "info_count_finished_recount": "All bins have been counted. Only Recount is available.", "info_upload_count": "Are you sure you want to upload all count data?", "info_upload_count_success": "Stock Count data uploaded successfully", "info_download_count_success": "Stock Count data downloaded successfully", "info_connect_to_dock": "Please ensure the device is docked, then click OK...", "loginId": "Login ID", "error": "Error", "version": "Version : ", "enterValidUrl": "Enter valid url", "enterValidUsername": "Enter valid username", "enterValidPassword": "Enter valid password", "pickURL": "Pick URL", "exit": "Exit", "enterLoginID": "Enter Login ID", "enterPassword": "Enter Password", "sendLogsToSerever": "SEND LOGS TO SERVER", "clearAllApplicationData": "CLEAR ALL APPLICATION DATA", "clearDataWarning": "This option will clear all the data associated with the application and restore the application to the freshly installed state. Deleted data cannot be recovered. Are you sure want to clear the data in the application", "logsSentToServer": "Logs successfully sent to Server", "refreshingMasterData": "Refreshing master data", "sabNewLandsBrewery": "SAB Newlands Brewery", "mainMenu": "Main Menu", "newCount": "1. New Count", "continueCount": "2. Continue Count", "uploadCount": "3. Upload Count", "logOff": "5. <PERSON><PERSON><PERSON>", "startCount": "Start Count", "breweryArea": "Brewery Area", "areaSelection": "Area Section", "forward": "Forward", "reverse": "Reverse", "count": "Count", "reCount": "Re-Count", "noResponseFromServer": "No Response from Server", "clearingExistingCounts": "Clearing existing counts", "area": "Area", "bin": "Bin", "section": "Section", "direction": "Direction", "sku": "SKU", "batch": "<PERSON><PERSON>", "expiryDate": "Expiry Date(ddmmy<PERSON><PERSON>y)", "majUom": "<PERSON>", "minUom": "<PERSON>", "unrestricted": "Unrestricted", "quality": "Quality", "blocked": "Blocked", "splitBin": "Split Bin", "save": "Save", "recountSKU": "Recount SKU", "changeSection": "Change Section", "na": "NA", "zero": "0", "seven": "7", "eight": "8", "nine": "9", "four": "4", "five": "5", "six": "6", "three": "3", "two": "2", "one": "1", "c": "C", "division": "/", "multiply": "X", "substraction": "-", "addition": "+", "equal": "=", "dot": ".", "setValue": "SET VALUE", "somethingWentWrongPleaseTryAgain": "Something went wrong.please try again.", "invalidNumber": "Invalid number!!!", "enterOrScanMaterial": "Enter/Scan material", "enterBatchValue": "Enter batch Value", "enterExpiredDate": "Enter Expired Date", "invalidDate": "Invalid date(ddmmyy)", "enterValue": "Enter Value", "uploading_count": "Uploading Count", "no_internet_connection": "No Internet connection. Make sure your device is connected to the network and try again.", "download_count": "4. Download Count", "continue_button": "Continue", "info_for_downloading_count": "Do you want to download counts ?", "downloading_count": "Downloading Count", "info_select_brewery_area": "Please make a selection for Brewery Area.", "info_for_empty_bin": "Are you sure the bin is empty?", "info_of_continue_count_for_download": "A counting process is currently in progress for the same Area and Section. Would you like to?", "proceed_button": "Proceed", "ignore_download_button": "Ignore Download"}

class AppUrl {
  final String name;
  final String url;
  final bool isDefault;

  const AppUrl({
    required this.name,
    required this.url,
    this.isDefault = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': this.name,
      'url': this.url,
      'default': this.isDefault,
    };
  }

  factory AppUrl.fromMap(Map<String, dynamic> map) {
    return AppUrl(
      name: map['name'] as String,
      url: map['url'] as String,
      isDefault: map['default'] as bool,
    );
  }

  @override
  List<Object?> get props => [name, url];
}

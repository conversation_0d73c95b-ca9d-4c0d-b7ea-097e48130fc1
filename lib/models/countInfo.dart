class CountInfo {
  String _area;
  String _section;
  String _direction;
  String _sequence;
  CountInfo(
      {required String area,
      required String section,
      required String direction,
      required String sequence})
      : _area = area,
        _section = section,
        _direction = direction,
        _sequence = sequence;

  String get area => _area;

  set area(String value) {
    _area = value;
  }

  String get section => _section;

  set section(String value) {
    _section = value;
  }

  String get direction => _direction;

  set direction(String value) {
    _direction = value;
  }

  String get sequence => _sequence;
  set sequence(String value) {
    _sequence = value;
  }
}

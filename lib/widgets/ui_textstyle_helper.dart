import 'dart:ui';

import 'package:flutter/cupertino.dart';

import '../utils/app_colors.dart';

class UITextStyleHelper {
  static textStyleLarge(BuildContext context, {FontWeight? fontWeight}) {
    return TextStyle(
        color: AppColor.black, fontSize: 25, fontWeight: fontWeight);
  }

  static textStyleLarge2(BuildContext context) {
    return TextStyle(fontSize: 20);
  }

  static textStyleLarge2Black(BuildContext context) {
    return TextStyle(color: AppColor.black, fontSize: 20);
  }

  static textStyleMedium(BuildContext context, {Color? color}) {
    return TextStyle(fontSize: 16, color: color);
  }

  static textStyleMediumBlack(BuildContext context) {
    return TextStyle(color: AppColor.black, fontSize: 16);
  }
}

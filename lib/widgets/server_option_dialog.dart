import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../models/app_url.dart';

class ServerUrlOptionDialog extends StatefulWidget {
  final String _title;
  final AppUrl _selectedUrl;
  final List<AppUrl> _serverList;
  final Function(AppUrl appUrl) _onUrlChanged;
  TextEditingController urlController;

  ServerUrlOptionDialog({
    Key? key,
    required title,
    required text,
    required selectedUrl,
    required serverList,
    required onUrlChanged,
    required this.urlController,
  })  : _title = title,
        _selectedUrl = selectedUrl,
        _serverList = serverList,
        _onUrlChanged = onUrlChanged,
        super(key: key);

  @override
  _ServerUrlOptionDialogState createState() => _ServerUrlOptionDialogState();
}

class _ServerUrlOptionDialogState extends State<ServerUrlOptionDialog> {
  static const double padding = 10;
  static const double avatarRadius = 45;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(padding),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  contentBox(context) {
    return Stack(
      children: <Widget>[
        Container(
          width: MediaQuery.of(context).size.width,
          padding: EdgeInsets.only(
              left: padding, top: padding, right: padding, bottom: padding),
          margin: EdgeInsets.only(top: avatarRadius),
          decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                    color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
              ]),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Padding(
                padding: EdgeInsets.only(
                    left: padding,
                    top: padding,
                    right: padding,
                    bottom: padding),
                child: Container(
                  child: Text(
                    widget._title,
                    style: TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
                  ),
                  alignment: Alignment.topLeft,
                ),
              ),
              SizedBox(
                height: 15,
              ),
              ListView.builder(
                shrinkWrap: true,
                itemCount: widget._serverList.length,
                itemBuilder: (context, index) {
                  return InkWell(
                    onTap: () {
                      widget._onUrlChanged(widget._serverList[index]);
                      widget.urlController.text = widget._serverList[index].url;
                      setState(() {});
                      Navigator.of(context).pop();
                    },
                    child: ListTile(
                        visualDensity:
                            VisualDensity(horizontal: 0, vertical: -4),
                        trailing: widget._selectedUrl.url ==
                                widget._serverList[index].url
                            ? Icon(
                                Icons.check_box,
                                color: Colors.black,
                              )
                            : null,
                        subtitle: Text(
                          widget._serverList[index].url,
                          //style: ScreenUtils.getValueLinkTextStyle(),
                        ),
                        title: Text(
                          widget._serverList[index].name,
                        )),
                  );
                },
              ),
              SizedBox(
                height: 22,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        'cancel',
                        style: TextStyle(fontSize: 18, color: Colors.black),
                      )),
                  TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        'search',
                        style: TextStyle(fontSize: 18, color: Colors.black),
                      )),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/cupertino.dart';
import 'package:nsts/widgets/ui_textstyle_helper.dart';

import '../utils/app_colors.dart';

class LabelValueWidget extends StatefulWidget {
  final String _label;
  final String _valueWidget;

  LabelValueWidget({Key? key, required label, required valueWidget})
      : _label = label,
        _valueWidget = valueWidget;

  @override
  _LabelValueWidgetState createState() => _LabelValueWidgetState();
}

class _LabelValueWidgetState extends State<LabelValueWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget._label,
          style: UITextStyleHelper.textStyleMedium(context, color: AppColor.grey),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 2),
          child: Text(
            widget._valueWidget,
            style: UITextStyleHelper.textStyleLarge2Black(context),
          ),
        )
      ],
    );
  }
}

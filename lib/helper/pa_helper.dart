import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:nsts/be/STOCK_COUNT_HEADER.dart';
import 'package:nsts/helper/ui_helper.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:unvired_ui/widgets/unvired_confirmation_dialog.dart';

import '../be/INPUT_DOWNLOAD_COUNT_HEADER.dart';
import '../utils/app_constants.dart';
import 'application_helper.dart';
import 'db_helper.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class PAHelper {
  static const sourceClass = 'PAHelper';
  static Future<Result?> downloadMasterDataMode(BuildContext context) async {
    Result? result;
    try {
      result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(ApplicationHelper.isAsync)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(umpApplicationFunctionName: AppConstants.PA_GET_MASTERDATA);
    } catch (e) {
      Logger.logError(sourceClass, "downloadMasterDataMode", e.toString());
      Navigator.pop(context);
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return UnviredConfirmationDialog(
                title: e.toString(),
                titleStyle: UIHelper().dialogTitleTextStyle(),
                positiveActionLabel: AppLocalizations.of(context)!.ok,
                onPositiveClickListener: () {
                  Navigator.pop(context);
                });
          });
    }
    return result;
  }

  static Future<Result?> uploadCount(BuildContext context) async {
    Result? result;
    STOCK_COUNT_HEADER? countHeader = await DBHelper().getCountHeader();
    // Map<String, dynamic> data = {
    //   AppConstants.BE_STOCK_COUNT: [
    //     {STOCK_COUNT_HEADER.TABLE_NAME: countHeader!.toJson()}
    //   ]
    // };
    try {
      result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(ApplicationHelper.isAsync)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.pull))
          .send(
              umpApplicationFunctionName: AppConstants.PA_UPLOAD,
              dataObject: countHeader!.toJson());
    } catch (e) {
      Logger.logError(sourceClass, "uploadCount", e.toString());
      Navigator.pop(context);
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return UnviredConfirmationDialog(
                title: e.toString(),
                titleStyle: UIHelper().dialogTitleTextStyle(),
                positiveActionLabel: AppLocalizations.of(context)!.ok,
                onPositiveClickListener: () {
                  Navigator.pop(context);
                });
          });
    }
    return result;
  }

  static Future<Result?> downloadCount(
      BuildContext context, INPUT_DOWNLOAD_COUNT_HEADER input_download_count_header) async {
    Result? result;
    try {
      result = await ((SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(ApplicationHelper.isAsync)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
              umpApplicationFunctionName: AppConstants.PA_DOWNLOAD,
              dataObject: input_download_count_header.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, "downloadCount", e.toString());
      Navigator.pop(context);
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return UnviredConfirmationDialog(
                title: e.toString(),
                titleStyle: UIHelper().dialogTitleTextStyle(),
                positiveActionLabel: AppLocalizations.of(context)!.ok,
                onPositiveClickListener: () {
                  Navigator.pop(context);
                });
          });
    }
    return result;
  }
}

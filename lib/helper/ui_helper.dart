import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../utils/app_colors.dart';

class UIHelper {
  TextStyle buttonTextStyle() {
    return TextStyle(fontSize: 16);
  }

  TextStyle dialogTitleTextStyle() {
    return TextStyle(color: AppColor.grey_600);
  }

  TextStyle textFieldTextStyle({Color? textColor}) {
    return TextStyle(color: textColor??AppColor.black, fontSize: 18);
  }

  void showCustomToast(BuildContext context, String message) {
    showToastWidget(
      Container(
        // constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.5),
        padding: EdgeInsets.symmetric(horizontal: 18, vertical: 18),
        margin: EdgeInsets.symmetric(horizontal: 50, vertical: 50),
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(),
          color: AppColor.black,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              message,
              style: TextStyle(color: Colors.white),
            ),
          ],
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
        ),
      ),
      context: context,
      isIgnoring: false,
      duration: Duration(seconds: 2),
      position: StyledToastPosition.center,
      animDuration: Duration.zero,
    );
  }

  getElevatedButton(
      {required BuildContext context,
      required Widget buttonName,
      required VoidCallback onPressCallBack,
      MaterialStateProperty<Color?>? buttonBackgroundColor}) {
    return Expanded(
        child: Container(
            height: 50,
            child: ElevatedButton(
                style: ButtonStyle(
                  backgroundColor: buttonBackgroundColor ?? MaterialStateProperty.all(AppColor.primaryDark),
                  shape: MaterialStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                ),
                onPressed: onPressCallBack,
                child: buttonName)));
  }
}

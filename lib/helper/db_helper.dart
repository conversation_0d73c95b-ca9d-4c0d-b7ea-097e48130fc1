import 'package:logger/logger.dart';
import 'package:nsts/be/STOCK_COUNT_ITEM.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../be/AREA_BIN_MAP.dart';
import '../be/AREA_COUNTSEQ_MAP.dart';
import '../be/BREWERY_HEADER.dart';
import '../be/MATERIAL_HEADER.dart';
import '../be/STOCK_COUNT_HEADER.dart';
import '../models/countInfo.dart';
import '../utils/constants.dart';

class DBHelper {
  static const sourceClass = 'DBHelper';

  Future<STOCK_COUNT_HEADER?> getCountHeader() async {
    try {
      List stockCountHeaders;
      stockCountHeaders = await AppDatabaseManager()
          .select(DBInputEntity(STOCK_COUNT_HEADER.TABLE_NAME, {}));
      if (stockCountHeaders.isNotEmpty && stockCountHeaders.length == 1) {
        return STOCK_COUNT_HEADER.fromJson(stockCountHeaders[0]);
      } else {
        STOCK_COUNT_HEADER? header = await createNewCountHeader();
        return header;
      }
    } catch (e) {
      Logger.logError(sourceClass, "getCountHeader", e.toString());
    }
    return null;
  }

  Future<List<STOCK_COUNT_ITEM>> getCountItems() async {
    try {
      List stockCountItems;
      List<STOCK_COUNT_ITEM> stockCountItemsData = [];
      stockCountItems = await AppDatabaseManager()
          .select(DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, {}));
      if (stockCountItems.isNotEmpty) {
        for (Map<String, dynamic> data in stockCountItems) {
          STOCK_COUNT_ITEM stock_count_item = STOCK_COUNT_ITEM.fromJson(data);
          stockCountItemsData.add(stock_count_item);
        }
        return stockCountItemsData;
      }
    } catch (e) {
      Logger.logError(sourceClass, "getCountHeader", e.toString());
    }
    return [];
  }

  Future<List<STOCK_COUNT_ITEM>> getCountItem(String area) async {
    String query = '';
    try {
      query = '${STOCK_COUNT_ITEM.FIELD_AREA} = "$area"';

      List stockCountItems;
      List<STOCK_COUNT_ITEM> stockCountItemsData = [];
      stockCountItems = await AppDatabaseManager().select(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, {})
            ..setWhereClause(query));
      if (stockCountItems.isNotEmpty) {
        for (Map<String, dynamic> data in stockCountItems) {
          STOCK_COUNT_ITEM stock_count_item = STOCK_COUNT_ITEM.fromJson(data);
          stockCountItemsData.add(stock_count_item);
        }
        return stockCountItemsData;
      }
    } catch (e) {
      Logger.logError(sourceClass, "getCountItem", e.toString());
    }
    return [];
  }

  Future<STOCK_COUNT_ITEM?> getCountBasedOnBin(
      String area, String areaSection, String direction, String bin,
  {int? countId}) async {
    try {
      String query =
          '${STOCK_COUNT_ITEM.FIELD_AREA} = "$area" AND ${STOCK_COUNT_ITEM.FIELD_AREASECTION} = "$areaSection"AND ${STOCK_COUNT_ITEM.FIELD_DIRECTION} = "$direction" AND ${STOCK_COUNT_ITEM.FIELD_BIN} = "$bin"';
      if (countId != null) {
        query += ' AND ${STOCK_COUNT_ITEM.FIELD_COUNT_ID} = $countId';
      }
      List stockCountItems;
      List<STOCK_COUNT_ITEM> stockCountItemsData = [];
      stockCountItems = await AppDatabaseManager().select(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, {})
            ..setWhereClause(query));
      if (stockCountItems.isNotEmpty) {
        for (Map<String, dynamic> data in stockCountItems) {
          STOCK_COUNT_ITEM stock_count_item = STOCK_COUNT_ITEM.fromJson(data);
          stockCountItemsData.add(stock_count_item);
        }
        return stockCountItemsData[0];
      }
    } catch (e) {
      Logger.logError(sourceClass, "getCountBasedOnBin", e.toString());
    }
    return null;
  }

  Future<STOCK_COUNT_HEADER?> createNewCountHeader() async {
    try {
      BREWERY_HEADER? breweryHeader = await getBreweryHeader();
      if (breweryHeader != null) {
        STOCK_COUNT_HEADER countHeader = STOCK_COUNT_HEADER(
            brewery_id: breweryHeader.brewery_id,
            desc: breweryHeader.brewery_desc);
        await AppDatabaseManager().insert(
            DBInputEntity(STOCK_COUNT_HEADER.TABLE_NAME, countHeader.toJson()));
        return getCountHeader();
      }
    } catch (e) {
      Logger.logError(sourceClass, 'createNewCountHeader', e.toString());
    }
    return null;
  }

  Future<BREWERY_HEADER?> getBreweryHeader() async {
    try {
      List breweryHeaders;
      breweryHeaders = await AppDatabaseManager()
          .select(DBInputEntity(BREWERY_HEADER.TABLE_NAME, {}));
      if (breweryHeaders.isNotEmpty && breweryHeaders.length > 0) {
        BREWERY_HEADER header = BREWERY_HEADER.fromJson(breweryHeaders[0]);
        return header;
      }
    } catch (e) {
      Logger.logError(sourceClass, 'getBreweryHeader', e.toString());
    }
    return null;
  }

  Future<void> clearStockCounts() async {
    try {
      String query = 'DELETE FROM ${STOCK_COUNT_ITEM.TABLE_NAME}';
      await AppDatabaseManager().execute(query);
    } catch (e) {
      Logger.logError(sourceClass, "clearStockCounts", e.toString());
    }
  }

  Future<void> clearStockCountItemss(
      String area, String breweryAreaSpinnerValue) async {
    String query = '';
    try {
      query =
          'DELETE FROM ${STOCK_COUNT_ITEM.TABLE_NAME} WHERE  ${STOCK_COUNT_ITEM.FIELD_AREA} = "$area"';
      if (breweryAreaSpinnerValue.isNotEmpty) {
        query =
            'DELETE FROM ${STOCK_COUNT_ITEM.TABLE_NAME} WHERE  ${STOCK_COUNT_ITEM.FIELD_AREA} = "$area" AND ${STOCK_COUNT_ITEM.FIELD_AREASECTION} = "$breweryAreaSpinnerValue"';
      }
      await AppDatabaseManager().execute(query);
    } catch (e) {
      Logger.logError(sourceClass, "clearStockCounts", e.toString());
    }
  }

  Future<void> clearStockCountItem(String area) async {
    String query = '';
    try {
      query =
          'DELETE FROM ${STOCK_COUNT_ITEM.TABLE_NAME} WHERE  ${STOCK_COUNT_ITEM.FIELD_AREA} = "$area"';
      await AppDatabaseManager().execute(query);
    } catch (e) {
      Logger.logError(sourceClass, "clearStockCounts", e.toString());
    }
  }

  Future<List<String>> getAllAreas() async {
    List allArea;
    List<String> allAreas = [];
    try {
      String query = "SELECT DISTINCT " +
          AREA_COUNTSEQ_MAP.FIELD_BREWERYAREA +
          " FROM " +
          AREA_COUNTSEQ_MAP.TABLE_NAME;
      allArea = await AppDatabaseManager().execute(query);
      for (Map<String, dynamic> data in allArea) {
        allAreas.add(
            data.values.toString().replaceAll(new RegExp(r'[^\w\s]+'), ''));
      }
    } catch (e) {
      Logger.logError(sourceClass, "getAllAreas", e.toString());
    }
    return allAreas;
  }

  Future<List<String>> getAllSections(String area) async {
    List allAreaSelections;
    List<String> allAreas = [];
    try {
      String query = "SELECT DISTINCT " +
          AREA_COUNTSEQ_MAP.FIELD_AREASECTION +
          "," +
          AREA_COUNTSEQ_MAP.FIELD_COUNTSEQUENCE +
          " FROM " +
          AREA_COUNTSEQ_MAP.TABLE_NAME +
          " WHERE " +
          AREA_COUNTSEQ_MAP.FIELD_BREWERYAREA +
          " = '" +
          area +
          "'";
      allAreaSelections = await AppDatabaseManager().execute(query);
      for (Map<String, dynamic> data in allAreaSelections) {
        allAreas.add(
            '${data[AREA_COUNTSEQ_MAP.FIELD_AREASECTION]}&${data[AREA_COUNTSEQ_MAP.FIELD_COUNTSEQUENCE]}');
      }
    } catch (e) {
      Logger.logError(sourceClass, "getAllSections", e.toString());
    }
    return allAreas;
  }

  Future<int> getCountOfStockCount(
      String area, String section, String direction) async {
    try {
      String query = STOCK_COUNT_ITEM.FIELD_AREA +
          " = '" +
          area +
          "' AND " +
          STOCK_COUNT_ITEM.FIELD_AREASECTION +
          " = '" +
          section +
          "' AND " +
          STOCK_COUNT_ITEM.FIELD_DIRECTION +
          " = '" +
          direction +
          "'";
      List items = await AppDatabaseManager().select(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, {})
            ..setWhereClause(query));
      return items.length;
    } catch (e) {
      Logger.logError(sourceClass, "getCountOfStockCount", e.toString());
    }
    return 0;
  }

  Future<bool> isCountFinished(CountInfo countInfo) async {
    bool result = (await getBin(countInfo, '') == null) &&
        (Constants.SEQUENCE_ROUTE == countInfo.sequence);
    return result;
  }

  Future<bool> isCurrentBinLastBin(CountInfo countInfo, String currentBin) async {
    try {
      // Get all remaining bins that haven't been counted yet (excluding the current bin)
      String query = "SELECT * FROM " +
          AREA_BIN_MAP.TABLE_NAME +
          " WHERE " +
          AREA_BIN_MAP.FIELD_BREWERYAREA +
          " = '" +
          countInfo.area +
          "' AND " +
          AREA_BIN_MAP.FIELD_AREASECTION +
          " = '" +
          countInfo.section +
          "' AND " +
          AREA_BIN_MAP.FIELD_BIN +
          " != '" +
          currentBin +
          "' AND " +
          AREA_BIN_MAP.FIELD_BIN +
          " NOT IN (SELECT " +
          STOCK_COUNT_ITEM.FIELD_BIN +
          " FROM " +
          STOCK_COUNT_ITEM.TABLE_NAME +
          " WHERE " +
          STOCK_COUNT_ITEM.FIELD_AREA +
          " = '" +
          countInfo.area +
          "' AND " +
          STOCK_COUNT_ITEM.FIELD_AREASECTION +
          " = '" +
          countInfo.section +
          "' AND " +
          STOCK_COUNT_ITEM.FIELD_DIRECTION +
          " = '" +
          countInfo.direction +
          "')" +
          " ORDER BY " +
          AREA_BIN_MAP.FIELD_BIN;

      var data = await AppDatabaseManager().execute(query);

      // If no remaining bins (excluding current), then current bin is the last one
      return data.isEmpty && (Constants.SEQUENCE_ROUTE == countInfo.sequence);
    } catch (e) {
      Logger.logError(sourceClass, "isCurrentBinLastBin", e.toString());
    }
    return false;
  }

  Future<AREA_BIN_MAP?> getBin(CountInfo countInfo, String bin) async {
    try {
      var data;
      String query;
      if (bin.isEmpty) {
        query = "SELECT * FROM " +
            AREA_BIN_MAP.TABLE_NAME +
            " WHERE " +
            AREA_BIN_MAP.FIELD_BREWERYAREA +
            " = '" +
            countInfo.area +
            "' AND " +
            AREA_BIN_MAP.FIELD_AREASECTION +
            " = '" +
            countInfo.section +
            "' AND " +
            AREA_BIN_MAP.FIELD_BIN +
            " NOT IN (SELECT " +
            STOCK_COUNT_ITEM.FIELD_BIN +
            " FROM " +
            STOCK_COUNT_ITEM.TABLE_NAME +
            " WHERE " +
            STOCK_COUNT_ITEM.FIELD_AREA +
            " = '" +
            countInfo.area +
            "' AND " +
            STOCK_COUNT_ITEM.FIELD_AREASECTION +
            " = '" +
            countInfo.section +
            "' AND " +
            STOCK_COUNT_ITEM.FIELD_DIRECTION +
            " = '" +
            countInfo.direction +
            "')" +
            " ORDER BY " +
            AREA_BIN_MAP.FIELD_BIN;
      } else {
        query = "SELECT * FROM " +
            AREA_BIN_MAP.TABLE_NAME +
            " WHERE " +
            AREA_BIN_MAP.FIELD_BIN +
            " = '" +
            bin +
            "'" +
            " ORDER BY " +
            AREA_BIN_MAP.FIELD_BIN;
      }
      data = await AppDatabaseManager().execute(query);
      if (data.length > 0) {
        for (Map<String, dynamic> areaData in data) {
          AREA_BIN_MAP areaBinData = AREA_BIN_MAP.fromJson(areaData);

          return areaBinData;
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, "getBin", e.toString());
    }
    return null;
  }

  Future<STOCK_COUNT_ITEM?> getNextItem(
      CountInfo countInfo, int currentCountId) async {
    try {
      List items;
      String query = STOCK_COUNT_ITEM.FIELD_AREA +
          " = '" +
          countInfo.area +
          "' AND " +
          STOCK_COUNT_ITEM.FIELD_AREASECTION +
          " = '" +
          countInfo.section +
          "' AND " +
          STOCK_COUNT_ITEM.FIELD_DIRECTION +
          " = '" +
          countInfo.direction +
          "'" +
          " ORDER BY " +
          STOCK_COUNT_ITEM.FIELD_COUNT_ID;
      items = await AppDatabaseManager().select(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, {})
            ..setWhereClause(query));
      if (items.isNotEmpty && items.length > 0) {
        for (int i = 0; i < items.length; i++) {
          STOCK_COUNT_ITEM item = STOCK_COUNT_ITEM.fromJson(items[i]);
          if (currentCountId == item.count_id) {
            int pos = getNextPosition(i, items.length);
            if (pos > i) {
              return STOCK_COUNT_ITEM.fromJson(items[pos]);
            } else if (pos < i && await isCountFinished(countInfo)) {
              return STOCK_COUNT_ITEM.fromJson(items[pos]);
            } else {
              return null;
            }
          }
        }
        if (await isCountFinished(countInfo)) {
          if (currentCountId <= 0) {
            return STOCK_COUNT_ITEM.fromJson(items[0]);
          }
          for (int i = 0; i < items.length; i++) {
            STOCK_COUNT_ITEM item = STOCK_COUNT_ITEM.fromJson(items[i]);
            if (currentCountId == item.count_id) {
              if (i + 1 == items.length) {
                return STOCK_COUNT_ITEM.fromJson(items[0]);
              } else {
                return STOCK_COUNT_ITEM.fromJson(items[1 + i]);
              }
            }
          }
        } else if (currentCountId != 0) {
          return STOCK_COUNT_ITEM.fromJson(items[0]);
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, "getNextItem", e.toString());
    }
    return null;
  }

  int getNextPosition(int pos, int length) {
    if (pos + 1 == length) {
      return 0;
    } else {
      return pos + 1;
    }
  }

  Future<MATERIAL_HEADER?> getMaterialHeader(String matNo) async {
    try {
      List headers;
      headers = await AppDatabaseManager().select(
          DBInputEntity(MATERIAL_HEADER.TABLE_NAME, {})
            ..setWhereClause(
                MATERIAL_HEADER.FIELD_MAT_NO + " = '" + matNo + "' LIMIT 1"));

      if (headers.isNotEmpty && headers.length > 0) {
        return MATERIAL_HEADER.fromJson(headers[0]);
      }
    } catch (e) {
      Logger.logError(sourceClass, "getMaterialHeader", e.toString());
    }
    return null;
  }

  Future<MATERIAL_HEADER?> getMaterialHeaderForInput(String input) async {
    try {
      List dataManager;
      dataManager = await AppDatabaseManager().select(
          DBInputEntity(MATERIAL_HEADER.TABLE_NAME, {})
            ..setWhereClause(MATERIAL_HEADER.FIELD_MAT_NO +
                " LIKE '%" +
                input +
                "%' OR " +
                MATERIAL_HEADER.FIELD_BARCODE +
                " = '" +
                input +
                "'" +
                " ORDER BY CAST(" +
                MATERIAL_HEADER.FIELD_MAT_NO +
                " AS INTEGER)"));
      if (dataManager.isNotEmpty && dataManager.length > 0) {
        return MATERIAL_HEADER.fromJson(dataManager[0]);
      }
    } catch (e) {
      Logger.logError(sourceClass, "getMaterialHeaderForInput", e.toString());
    }
    return null;
  }

  Future<STOCK_COUNT_ITEM?> getPrevItem(
      CountInfo countInfo, int currentCountId) async {
    try {
      List items;
      items = await AppDatabaseManager().select(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, {})
            ..setWhereClause(STOCK_COUNT_ITEM.FIELD_AREA +
                " = '" +
                countInfo.area +
                "' AND " +
                STOCK_COUNT_ITEM.FIELD_AREASECTION +
                " = '" +
                countInfo.section +
                "' AND " +
                STOCK_COUNT_ITEM.FIELD_DIRECTION +
                " = '" +
                countInfo.direction +
                "'" +
                " ORDER BY " +
                STOCK_COUNT_ITEM.FIELD_COUNT_ID));

      if (items.isNotEmpty && items.length > 0) {
        for (int i = items.length - 1; i >= 0; i--) {
          STOCK_COUNT_ITEM item = STOCK_COUNT_ITEM.fromJson(items[i]);
          if (currentCountId == item.count_id) {
            int pos = getPrevPosition(i, items.length);
            if (pos < i) {
              return STOCK_COUNT_ITEM.fromJson(items[pos]);
            } else if (pos > i && await isCountFinished(countInfo)) {
              return STOCK_COUNT_ITEM.fromJson(items[pos]);
            } else {
              return null;
            }
          }
        }

        if (!await isCountFinished(countInfo)) {
          if (currentCountId >
              (STOCK_COUNT_ITEM
                  .fromJson(items[items.length - 1])
                  .count_id!
                  .toInt())) {
            return (STOCK_COUNT_ITEM.fromJson(items[items.length - 1]));
          } else {
            return null; //not yet
          }
        } else {
          for (int i = 0; i < items.length; i++) {
            STOCK_COUNT_ITEM item = STOCK_COUNT_ITEM.fromJson(items[i]);
            if (currentCountId == item.count_id) {
              if (i == 0) {
                return STOCK_COUNT_ITEM.fromJson(items[items.length - 1]);
              } else {
                return STOCK_COUNT_ITEM.fromJson(items[i - 1]);
              }
            }
          }
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, "getPrevItem", e.toString());
    }

    return null;
  }

  int getPrevPosition(int pos, int length) {
    if (pos == 0) {
      return length - 1;
    } else {
      return pos - 1;
    }
  }

  Future<int> getNextCountId() async {
    int countId = 0;
    try {
      String query =
          "SELECT MAX(${STOCK_COUNT_ITEM.FIELD_COUNT_ID}) AS ${STOCK_COUNT_ITEM.FIELD_COUNT_ID} FROM ${STOCK_COUNT_ITEM.TABLE_NAME}";
      List<Map<String, dynamic>> result =
          await AppDatabaseManager().execute(query);
      countId = result[0][STOCK_COUNT_ITEM.FIELD_COUNT_ID];
    } catch (e) {
      Logger.logError(sourceClass, "getNextCountId", e.toString());
    }
    return ++countId;
  }

  Future<int> getTotalFinishedCount(CountInfo countInfo) async {
    try {
      List count = await AppDatabaseManager().select(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, {})
            ..setWhereClause(STOCK_COUNT_ITEM.FIELD_AREA +
                " = '" +
                countInfo.area +
                "' AND " +
                STOCK_COUNT_ITEM.FIELD_AREASECTION +
                " = '" +
                countInfo.section +
                "' AND " +
                STOCK_COUNT_ITEM.FIELD_DIRECTION +
                " = '" +
                countInfo.direction +
                "'"));
      return count.length;
    } catch (e) {
      Logger.logError(sourceClass, "getTotalFinishedCount", e.toString());
    }
    return 0;
  }

  Future<int> getCountPosition(CountInfo countInfo, int currentCountId) async {
    try {
      List count = await AppDatabaseManager().select(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, {})
            ..setWhereClause(STOCK_COUNT_ITEM.FIELD_AREA +
                " = '" +
                countInfo.area +
                "' AND " +
                STOCK_COUNT_ITEM.FIELD_AREASECTION +
                " = '" +
                countInfo.section +
                "' AND " +
                STOCK_COUNT_ITEM.FIELD_DIRECTION +
                " = '" +
                countInfo.direction +
                "'" +
                " ORDER BY " +
                STOCK_COUNT_ITEM.FIELD_COUNT_ID));
      if (count.isNotEmpty && count.length > 0) {
        for (int i = 0; i < count.length; i++) {
          STOCK_COUNT_ITEM item = STOCK_COUNT_ITEM.fromJson(count[i]);
          if (currentCountId == item.count_id) {
            return i + 1;
          }
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, "getCountPosition", e.toString());
    }
    return 0;
  }

  Future<void> insertCountItem(STOCK_COUNT_ITEM stockCountItem) async {
    try {
      await AppDatabaseManager().insert(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, stockCountItem.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, "insertOrUpdateCountItem", e.toString());
    }
  }

  Future<void> updateCountItem(STOCK_COUNT_ITEM stockCountItem) async {
    try {
      await AppDatabaseManager().update(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, stockCountItem.toJson()));
    } catch (e) {
      Logger.logError(sourceClass, "insertOrUpdateCountItem", e.toString());
    }
  }

  Future<String> getMaxBinLetter(CountInfo countInfo) async {
    try {
      List data = await AppDatabaseManager().select(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, {})
            ..setWhereClause(STOCK_COUNT_ITEM.FIELD_AREA +
                " = '" +
                countInfo.area +
                "' AND " +
                STOCK_COUNT_ITEM.FIELD_AREASECTION +
                " = '" +
                countInfo.section +
                "' ORDER BY ${STOCK_COUNT_ITEM.FIELD_BIN} DESC"));

      if (data.isEmpty || data.length <= 0) {
        return "";
      }

      return (STOCK_COUNT_ITEM.fromJson(data[0])).bin!;
    } catch (e) {
      Logger.logError(sourceClass, "getMaxBinLetter", e.toString());
    }

    return "";
  }

  Future<String> getMaxSubBinLetter(CountInfo countInfo, String bin) async {
    try {
      List data = await AppDatabaseManager().select(
          DBInputEntity(STOCK_COUNT_ITEM.TABLE_NAME, {})
            ..setWhereClause(STOCK_COUNT_ITEM.FIELD_AREA +
                " = '" +
                countInfo.area +
                "' AND " +
                STOCK_COUNT_ITEM.FIELD_AREASECTION +
                " = '" +
                countInfo.section +
                "' AND " +
                STOCK_COUNT_ITEM.FIELD_BIN +
                " LIKE '" +
                bin +
                "%' ORDER BY ${STOCK_COUNT_ITEM.FIELD_BIN} DESC"));
      if (data.isEmpty || data.length <= 0) {
        return "";
      }
      return (STOCK_COUNT_ITEM.fromJson(data[0])).bin!;
    } catch (e) {
      Logger.logError(sourceClass, "", e.toString());
    }
    return "";
  }

  Future<void> clearRecountStockCount(
      String area, String section, String direction, String materialNo) async {
    try {
      await AppDatabaseManager().execute(
          'DELETE FROM ${STOCK_COUNT_ITEM.TABLE_NAME} WHERE ${STOCK_COUNT_ITEM.FIELD_AREA} = "'
          "${area}"
          '" AND ${STOCK_COUNT_ITEM.FIELD_AREASECTION} = "'
          "${section}"
          '"  AND ${STOCK_COUNT_ITEM.FIELD_DIRECTION} = "'
          "${direction}"
          '" AND ${STOCK_COUNT_ITEM.FIELD_MAT_NO} = "'
          "${materialNo}"
          '"');
    } catch (e) {
      Logger.logError(sourceClass, "clearStockCount", e.toString());
    }
  }

  Future<void> clearRecountStockCounts(
      String area, String section, String direction) async {
    try {
      await AppDatabaseManager().execute(
          'DELETE FROM ${STOCK_COUNT_ITEM.TABLE_NAME} WHERE ${STOCK_COUNT_ITEM.FIELD_AREA} = "'
          "${area}"
          '" AND ${STOCK_COUNT_ITEM.FIELD_AREASECTION} = "'
          "${section}"
          '"  AND ${STOCK_COUNT_ITEM.FIELD_DIRECTION} = "'
          "${direction}"
          '" ');
    } catch (e) {
      Logger.logError(sourceClass, "clearStockCount", e.toString());
    }
  }
}

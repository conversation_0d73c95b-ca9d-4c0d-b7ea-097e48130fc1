// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `NSTS`
  String get appName {
    return Intl.message(
      'NSTS',
      name: 'appName',
      desc: '',
      args: [],
    );
  }

  /// `Please Enter Your Password To Login`
  String get pleaseEnterYourPasswordToLogin {
    return Intl.message(
      'Please Enter Your Password To Login',
      name: 'pleaseEnterYourPasswordToLogin',
      desc: '',
      args: [],
    );
  }

  /// `Please Select Account To Continue`
  String get pleaseSelectAccountToContinue {
    return Intl.message(
      'Please Select Account To Continue',
      name: 'pleaseSelectAccountToContinue',
      desc: '',
      args: [],
    );
  }

  /// `Please Wait...`
  String get pleaseWaitThreeDots {
    return Intl.message(
      'Please Wait...',
      name: 'pleaseWaitThreeDots',
      desc: '',
      args: [],
    );
  }

  /// `Unvired Login`
  String get unviredLogin {
    return Intl.message(
      'Unvired Login',
      name: 'unviredLogin',
      desc: '',
      args: [],
    );
  }

  /// `YES`
  String get yes {
    return Intl.message(
      'YES',
      name: 'yes',
      desc: '',
      args: [],
    );
  }

  /// `Url`
  String get url {
    return Intl.message(
      'Url',
      name: 'url',
      desc: '',
      args: [],
    );
  }

  /// `Username`
  String get userName {
    return Intl.message(
      'Username',
      name: 'userName',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message(
      'Password',
      name: 'password',
      desc: '',
      args: [],
    );
  }

  /// `Login`
  String get login {
    return Intl.message(
      'Login',
      name: 'login',
      desc: '',
      args: [],
    );
  }

  /// `nSTS`
  String get app_name {
    return Intl.message(
      'nSTS',
      name: 'app_name',
      desc: '',
      args: [],
    );
  }

  /// `STOCK TAKE SYSTEM`
  String get stockTakeSystem {
    return Intl.message(
      'STOCK TAKE SYSTEM',
      name: 'stockTakeSystem',
      desc: '',
      args: [],
    );
  }

  /// `SAB Newlands Brewery`
  String get home_title {
    return Intl.message(
      'SAB Newlands Brewery',
      name: 'home_title',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get action_settings {
    return Intl.message(
      'Settings',
      name: 'action_settings',
      desc: '',
      args: [],
    );
  }

  /// `OK`
  String get ok {
    return Intl.message(
      'OK',
      name: 'ok',
      desc: '',
      args: [],
    );
  }

  /// `CANCEL`
  String get cancel {
    return Intl.message(
      'CANCEL',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `NO`
  String get no {
    return Intl.message(
      'NO',
      name: 'no',
      desc: '',
      args: [],
    );
  }

  /// `Options`
  String get options {
    return Intl.message(
      'Options',
      name: 'options',
      desc: '',
      args: [],
    );
  }

  /// `Clear all application data`
  String get reset_data {
    return Intl.message(
      'Clear all application data',
      name: 'reset_data',
      desc: '',
      args: [],
    );
  }

  /// `This option will clear all the data associated with the application and restore the application to the freshly installed state. Deleted data cannot be recovered. Are you sure want to clear the data in the application`
  String get reset_warn {
    return Intl.message(
      'This option will clear all the data associated with the application and restore the application to the freshly installed state. Deleted data cannot be recovered. Are you sure want to clear the data in the application',
      name: 'reset_warn',
      desc: '',
      args: [],
    );
  }

  /// `>SEND LOGS TO SERVER`
  String get sendLogs {
    return Intl.message(
      '>SEND LOGS TO SERVER',
      name: 'sendLogs',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get confirm {
    return Intl.message(
      'Confirm',
      name: 'confirm',
      desc: '',
      args: [],
    );
  }

  /// `Warning`
  String get warning {
    return Intl.message(
      'Warning',
      name: 'warning',
      desc: '',
      args: [],
    );
  }

  /// `Information`
  String get info {
    return Intl.message(
      'Information',
      name: 'info',
      desc: '',
      args: [],
    );
  }

  /// `Please wait`
  String get please_wait {
    return Intl.message(
      'Please wait',
      name: 'please_wait',
      desc: '',
      args: [],
    );
  }

  /// `No Response from Server`
  String get no_server_response {
    return Intl.message(
      'No Response from Server',
      name: 'no_server_response',
      desc: '',
      args: [],
    );
  }

  /// `Refreshing master data`
  String get info_masterdata_refresh {
    return Intl.message(
      'Refreshing master data',
      name: 'info_masterdata_refresh',
      desc: '',
      args: [],
    );
  }

  /// `All existing counts will be cleared. Do you wish to continue?`
  String get info_clear_all_count {
    return Intl.message(
      'All existing counts will be cleared. Do you wish to continue?',
      name: 'info_clear_all_count',
      desc: '',
      args: [],
    );
  }

  /// `Clearing existing counts&#8230;`
  String get info_clearing_count {
    return Intl.message(
      'Clearing existing counts&#8230;',
      name: 'info_clearing_count',
      desc: '',
      args: [],
    );
  }

  /// `There is an existing count for this area and section. Do you wish to continue the count?`
  String get info_clear_for_new_count {
    return Intl.message(
      'There is an existing count for this area and section. Do you wish to continue the count?',
      name: 'info_clear_for_new_count',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to clear all count information for area and section?`
  String get info_clear_for_recount {
    return Intl.message(
      'Do you want to clear all count information for area and section?',
      name: 'info_clear_for_recount',
      desc: '',
      args: [],
    );
  }

  /// `Please make a selection for Area.`
  String get info_select_area {
    return Intl.message(
      'Please make a selection for Area.',
      name: 'info_select_area',
      desc: '',
      args: [],
    );
  }

  /// `Please make a selection for Section.`
  String get info_select_section {
    return Intl.message(
      'Please make a selection for Section.',
      name: 'info_select_section',
      desc: '',
      args: [],
    );
  }

  /// `This will clear all counts for this material in this area and section. Do you want to re-count?`
  String get info_re_count {
    return Intl.message(
      'This will clear all counts for this material in this area and section. Do you want to re-count?',
      name: 'info_re_count',
      desc: '',
      args: [],
    );
  }

  /// `Decimals can only exist in the Minor UOM field. Can the decimals be placed in the Minor UOM field for this result?`
  String get info_decimal_value {
    return Intl.message(
      'Decimals can only exist in the Minor UOM field. Can the decimals be placed in the Minor UOM field for this result?',
      name: 'info_decimal_value',
      desc: '',
      args: [],
    );
  }

  /// `All bins have been counted. Returning to Main Menu.`
  String get info_count_finished {
    return Intl.message(
      'All bins have been counted. Returning to Main Menu.',
      name: 'info_count_finished',
      desc: '',
      args: [],
    );
  }

  /// `All bins have been counted. Only Recount is available.`
  String get info_count_finished_recount {
    return Intl.message(
      'All bins have been counted. Only Recount is available.',
      name: 'info_count_finished_recount',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to upload all count data?`
  String get info_upload_count {
    return Intl.message(
      'Are you sure you want to upload all count data?',
      name: 'info_upload_count',
      desc: '',
      args: [],
    );
  }

  /// `Stock Count data uploaded successfully`
  String get info_upload_count_success {
    return Intl.message(
      'Stock Count data uploaded successfully',
      name: 'info_upload_count_success',
      desc: '',
      args: [],
    );
  }

  /// `Please ensure the device is docked, then click OK...`
  String get info_connect_to_dock {
    return Intl.message(
      'Please ensure the device is docked, then click OK...',
      name: 'info_connect_to_dock',
      desc: '',
      args: [],
    );
  }

  /// `Login ID`
  String get loginId {
    return Intl.message(
      'Login ID',
      name: 'loginId',
      desc: '',
      args: [],
    );
  }

  /// `Error`
  String get error {
    return Intl.message(
      'Error',
      name: 'error',
      desc: '',
      args: [],
    );
  }

  /// `Version : `
  String get version {
    return Intl.message(
      'Version : ',
      name: 'version',
      desc: '',
      args: [],
    );
  }

  /// `Enter valid url`
  String get enterValidUrl {
    return Intl.message(
      'Enter valid url',
      name: 'enterValidUrl',
      desc: '',
      args: [],
    );
  }

  /// `Enter valid username`
  String get enterValidUsername {
    return Intl.message(
      'Enter valid username',
      name: 'enterValidUsername',
      desc: '',
      args: [],
    );
  }

  /// `Enter valid password`
  String get enterValidPassword {
    return Intl.message(
      'Enter valid password',
      name: 'enterValidPassword',
      desc: '',
      args: [],
    );
  }

  /// `Pick URL`
  String get pickURL {
    return Intl.message(
      'Pick URL',
      name: 'pickURL',
      desc: '',
      args: [],
    );
  }

  /// `Exit`
  String get exit {
    return Intl.message(
      'Exit',
      name: 'exit',
      desc: '',
      args: [],
    );
  }

  /// `Enter Login ID`
  String get enterLoginID {
    return Intl.message(
      'Enter Login ID',
      name: 'enterLoginID',
      desc: '',
      args: [],
    );
  }

  /// `Enter Password`
  String get enterPassword {
    return Intl.message(
      'Enter Password',
      name: 'enterPassword',
      desc: '',
      args: [],
    );
  }

  /// `SEND LOGS TO SERVER`
  String get sendLogsToSerever {
    return Intl.message(
      'SEND LOGS TO SERVER',
      name: 'sendLogsToSerever',
      desc: '',
      args: [],
    );
  }

  /// `CLEAR ALL APPLICATION DATA`
  String get clearAllApplicationData {
    return Intl.message(
      'CLEAR ALL APPLICATION DATA',
      name: 'clearAllApplicationData',
      desc: '',
      args: [],
    );
  }

  /// `This option will clear all the data associated with the application and restore the application to the freshly installed state. Deleted data cannot be recovered. Are you sure want to clear the data in the application`
  String get clearDataWarning {
    return Intl.message(
      'This option will clear all the data associated with the application and restore the application to the freshly installed state. Deleted data cannot be recovered. Are you sure want to clear the data in the application',
      name: 'clearDataWarning',
      desc: '',
      args: [],
    );
  }

  /// `Logs successfully sent to Server`
  String get logsSentToServer {
    return Intl.message(
      'Logs successfully sent to Server',
      name: 'logsSentToServer',
      desc: '',
      args: [],
    );
  }

  /// `Refreshing master data`
  String get refreshingMasterData {
    return Intl.message(
      'Refreshing master data',
      name: 'refreshingMasterData',
      desc: '',
      args: [],
    );
  }

  /// `SAB Newlands Brewery`
  String get sabNewLandsBrewery {
    return Intl.message(
      'SAB Newlands Brewery',
      name: 'sabNewLandsBrewery',
      desc: '',
      args: [],
    );
  }

  /// `Main Menu`
  String get mainMenu {
    return Intl.message(
      'Main Menu',
      name: 'mainMenu',
      desc: '',
      args: [],
    );
  }

  /// `1. New Count`
  String get newCount {
    return Intl.message(
      '1. New Count',
      name: 'newCount',
      desc: '',
      args: [],
    );
  }

  /// `2. Continue Count`
  String get continueCount {
    return Intl.message(
      '2. Continue Count',
      name: 'continueCount',
      desc: '',
      args: [],
    );
  }

  /// `3. Upload Count`
  String get uploadCount {
    return Intl.message(
      '3. Upload Count',
      name: 'uploadCount',
      desc: '',
      args: [],
    );
  }

  /// `5. Logoff`
  String get logOff {
    return Intl.message(
      '5. Logoff',
      name: 'logOff',
      desc: '',
      args: [],
    );
  }

  /// `Start Count`
  String get startCount {
    return Intl.message(
      'Start Count',
      name: 'startCount',
      desc: '',
      args: [],
    );
  }

  /// `Brewery Area`
  String get breweryArea {
    return Intl.message(
      'Brewery Area',
      name: 'breweryArea',
      desc: '',
      args: [],
    );
  }

  /// `Area Section`
  String get areaSelection {
    return Intl.message(
      'Area Section',
      name: 'areaSelection',
      desc: '',
      args: [],
    );
  }

  /// `Forward`
  String get forward {
    return Intl.message(
      'Forward',
      name: 'forward',
      desc: '',
      args: [],
    );
  }

  /// `Reverse`
  String get reverse {
    return Intl.message(
      'Reverse',
      name: 'reverse',
      desc: '',
      args: [],
    );
  }

  /// `Count`
  String get count {
    return Intl.message(
      'Count',
      name: 'count',
      desc: '',
      args: [],
    );
  }

  /// `Re-Count`
  String get reCount {
    return Intl.message(
      'Re-Count',
      name: 'reCount',
      desc: '',
      args: [],
    );
  }

  /// `No Response from Server`
  String get noResponseFromServer {
    return Intl.message(
      'No Response from Server',
      name: 'noResponseFromServer',
      desc: '',
      args: [],
    );
  }

  /// `Clearing existing counts`
  String get clearingExistingCounts {
    return Intl.message(
      'Clearing existing counts',
      name: 'clearingExistingCounts',
      desc: '',
      args: [],
    );
  }

  /// `Area`
  String get area {
    return Intl.message(
      'Area',
      name: 'area',
      desc: '',
      args: [],
    );
  }

  /// `Bin`
  String get bin {
    return Intl.message(
      'Bin',
      name: 'bin',
      desc: '',
      args: [],
    );
  }

  /// `Section`
  String get section {
    return Intl.message(
      'Section',
      name: 'section',
      desc: '',
      args: [],
    );
  }

  /// `Direction`
  String get direction {
    return Intl.message(
      'Direction',
      name: 'direction',
      desc: '',
      args: [],
    );
  }

  /// `SKU`
  String get sku {
    return Intl.message(
      'SKU',
      name: 'sku',
      desc: '',
      args: [],
    );
  }

  /// `Batch`
  String get batch {
    return Intl.message(
      'Batch',
      name: 'batch',
      desc: '',
      args: [],
    );
  }

  /// `Expiry Date(ddmmyyyy)`
  String get expiryDate {
    return Intl.message(
      'Expiry Date(ddmmyyyy)',
      name: 'expiryDate',
      desc: '',
      args: [],
    );
  }

  /// `Maj Uom`
  String get majUom {
    return Intl.message(
      'Maj Uom',
      name: 'majUom',
      desc: '',
      args: [],
    );
  }

  /// `Min Uom`
  String get minUom {
    return Intl.message(
      'Min Uom',
      name: 'minUom',
      desc: '',
      args: [],
    );
  }

  /// `Unrestricted`
  String get unrestricted {
    return Intl.message(
      'Unrestricted',
      name: 'unrestricted',
      desc: '',
      args: [],
    );
  }

  /// `Quality`
  String get quality {
    return Intl.message(
      'Quality',
      name: 'quality',
      desc: '',
      args: [],
    );
  }

  /// `Blocked`
  String get blocked {
    return Intl.message(
      'Blocked',
      name: 'blocked',
      desc: '',
      args: [],
    );
  }

  /// `Split Bin`
  String get splitBin {
    return Intl.message(
      'Split Bin',
      name: 'splitBin',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get save {
    return Intl.message(
      'Save',
      name: 'save',
      desc: '',
      args: [],
    );
  }

  /// `Recount SKU`
  String get recountSKU {
    return Intl.message(
      'Recount SKU',
      name: 'recountSKU',
      desc: '',
      args: [],
    );
  }

  /// `Change Section`
  String get changeSection {
    return Intl.message(
      'Change Section',
      name: 'changeSection',
      desc: '',
      args: [],
    );
  }

  /// `NA`
  String get na {
    return Intl.message(
      'NA',
      name: 'na',
      desc: '',
      args: [],
    );
  }

  /// `0`
  String get zero {
    return Intl.message(
      '0',
      name: 'zero',
      desc: '',
      args: [],
    );
  }

  /// `7`
  String get seven {
    return Intl.message(
      '7',
      name: 'seven',
      desc: '',
      args: [],
    );
  }

  /// `8`
  String get eight {
    return Intl.message(
      '8',
      name: 'eight',
      desc: '',
      args: [],
    );
  }

  /// `9`
  String get nine {
    return Intl.message(
      '9',
      name: 'nine',
      desc: '',
      args: [],
    );
  }

  /// `4`
  String get four {
    return Intl.message(
      '4',
      name: 'four',
      desc: '',
      args: [],
    );
  }

  /// `5`
  String get five {
    return Intl.message(
      '5',
      name: 'five',
      desc: '',
      args: [],
    );
  }

  /// `6`
  String get six {
    return Intl.message(
      '6',
      name: 'six',
      desc: '',
      args: [],
    );
  }

  /// `3`
  String get three {
    return Intl.message(
      '3',
      name: 'three',
      desc: '',
      args: [],
    );
  }

  /// `2`
  String get two {
    return Intl.message(
      '2',
      name: 'two',
      desc: '',
      args: [],
    );
  }

  /// `1`
  String get one {
    return Intl.message(
      '1',
      name: 'one',
      desc: '',
      args: [],
    );
  }

  /// `C`
  String get c {
    return Intl.message(
      'C',
      name: 'c',
      desc: '',
      args: [],
    );
  }

  /// `/`
  String get division {
    return Intl.message(
      '/',
      name: 'division',
      desc: '',
      args: [],
    );
  }

  /// `X`
  String get multiply {
    return Intl.message(
      'X',
      name: 'multiply',
      desc: '',
      args: [],
    );
  }

  /// `-`
  String get substraction {
    return Intl.message(
      '-',
      name: 'substraction',
      desc: '',
      args: [],
    );
  }

  /// `+`
  String get addition {
    return Intl.message(
      '+',
      name: 'addition',
      desc: '',
      args: [],
    );
  }

  /// `=`
  String get equal {
    return Intl.message(
      '=',
      name: 'equal',
      desc: '',
      args: [],
    );
  }

  /// `.`
  String get dot {
    return Intl.message(
      '.',
      name: 'dot',
      desc: '',
      args: [],
    );
  }

  /// `SET VALUE`
  String get setValue {
    return Intl.message(
      'SET VALUE',
      name: 'setValue',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong.please try again.`
  String get somethingWentWrongPleaseTryAgain {
    return Intl.message(
      'Something went wrong.please try again.',
      name: 'somethingWentWrongPleaseTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Invalid number!!!`
  String get invalidNumber {
    return Intl.message(
      'Invalid number!!!',
      name: 'invalidNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter/Scan material`
  String get enterOrScanMaterial {
    return Intl.message(
      'Enter/Scan material',
      name: 'enterOrScanMaterial',
      desc: '',
      args: [],
    );
  }

  /// `Enter batch Value`
  String get enterBatchValue {
    return Intl.message(
      'Enter batch Value',
      name: 'enterBatchValue',
      desc: '',
      args: [],
    );
  }

  /// `Enter Expired Date`
  String get enterExpiredDate {
    return Intl.message(
      'Enter Expired Date',
      name: 'enterExpiredDate',
      desc: '',
      args: [],
    );
  }

  /// `Invalid date(ddmmyy)`
  String get invalidDate {
    return Intl.message(
      'Invalid date(ddmmyy)',
      name: 'invalidDate',
      desc: '',
      args: [],
    );
  }

  /// `Enter Value`
  String get enterValue {
    return Intl.message(
      'Enter Value',
      name: 'enterValue',
      desc: '',
      args: [],
    );
  }

  /// `Uploading Count`
  String get uploading_count {
    return Intl.message(
      'Uploading Count',
      name: 'uploading_count',
      desc: '',
      args: [],
    );
  }

  /// `No Internet connection. Make sure your device is connected to the network and try again.`
  String get no_internet_connection {
    return Intl.message(
      'No Internet connection. Make sure your device is connected to the network and try again.',
      name: 'no_internet_connection',
      desc: '',
      args: [],
    );
  }

  /// `4. Download Count`
  String get download_count {
    return Intl.message(
      '4. Download Count',
      name: 'download_count',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continue_button {
    return Intl.message(
      'Continue',
      name: 'continue_button',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to download counts ?`
  String get info_for_downloading_count {
    return Intl.message(
      'Do you want to download counts ?',
      name: 'info_for_downloading_count',
      desc: '',
      args: [],
    );
  }

  /// `Downloading Count`
  String get downloading_count {
    return Intl.message(
      'Downloading Count',
      name: 'downloading_count',
      desc: '',
      args: [],
    );
  }

  /// `Please make a selection for Brewery Area.`
  String get info_select_brewery_area {
    return Intl.message(
      'Please make a selection for Brewery Area.',
      name: 'info_select_brewery_area',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure the bin is empty?`
  String get info_for_empty_bin {
    return Intl.message(
      'Are you sure the bin is empty?',
      name: 'info_for_empty_bin',
      desc: '',
      args: [],
    );
  }

  /// `A counting process is currently in progress for the same Area and Section. Would you like to?`
  String get info_of_continue_count_for_download {
    return Intl.message(
      'A counting process is currently in progress for the same Area and Section. Would you like to?',
      name: 'info_of_continue_count_for_download',
      desc: '',
      args: [],
    );
  }

  /// `Proceed`
  String get proceed_button {
    return Intl.message(
      'Proceed',
      name: 'proceed_button',
      desc: '',
      args: [],
    );
  }

  /// `Ignore Download`
  String get ignore_download_button {
    return Intl.message(
      'Ignore Download',
      name: 'ignore_download_button',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'es'),
      Locale.fromSubtags(languageCode: 'hi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}

// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "action_settings": MessageLookupByLibrary.simpleMessage("Settings"),
        "addition": MessageLookupByLibrary.simpleMessage("+"),
        "appName": MessageLookupByLibrary.simpleMessage("NSTS"),
        "app_name": MessageLookupByLibrary.simpleMessage("nSTS"),
        "area": MessageLookupByLibrary.simpleMessage("Area"),
        "areaSelection": MessageLookupByLibrary.simpleMessage("Area Section"),
        "batch": MessageLookupByLibrary.simpleMessage("Batch"),
        "bin": MessageLookupByLibrary.simpleMessage("Bin"),
        "blocked": MessageLookupByLibrary.simpleMessage("Blocked"),
        "breweryArea": MessageLookupByLibrary.simpleMessage("Brewery Area"),
        "c": MessageLookupByLibrary.simpleMessage("C"),
        "cancel": MessageLookupByLibrary.simpleMessage("CANCEL"),
        "changeSection": MessageLookupByLibrary.simpleMessage("Change Section"),
        "clearAllApplicationData":
            MessageLookupByLibrary.simpleMessage("CLEAR ALL APPLICATION DATA"),
        "clearDataWarning": MessageLookupByLibrary.simpleMessage(
            "This option will clear all the data associated with the application and restore the application to the freshly installed state. Deleted data cannot be recovered. Are you sure want to clear the data in the application"),
        "clearingExistingCounts":
            MessageLookupByLibrary.simpleMessage("Clearing existing counts"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "continueCount":
            MessageLookupByLibrary.simpleMessage("2. Continue Count"),
        "continue_button": MessageLookupByLibrary.simpleMessage("Continue"),
        "count": MessageLookupByLibrary.simpleMessage("Count"),
        "direction": MessageLookupByLibrary.simpleMessage("Direction"),
        "division": MessageLookupByLibrary.simpleMessage("/"),
        "dot": MessageLookupByLibrary.simpleMessage("."),
        "download_count":
            MessageLookupByLibrary.simpleMessage("4. Download Count"),
        "downloading_count":
            MessageLookupByLibrary.simpleMessage("Downloading Count"),
        "eight": MessageLookupByLibrary.simpleMessage("8"),
        "enterBatchValue":
            MessageLookupByLibrary.simpleMessage("Enter batch Value"),
        "enterExpiredDate":
            MessageLookupByLibrary.simpleMessage("Enter Expired Date"),
        "enterLoginID": MessageLookupByLibrary.simpleMessage("Enter Login ID"),
        "enterOrScanMaterial":
            MessageLookupByLibrary.simpleMessage("Enter/Scan material"),
        "enterPassword": MessageLookupByLibrary.simpleMessage("Enter Password"),
        "enterValidPassword":
            MessageLookupByLibrary.simpleMessage("Enter valid password"),
        "enterValidUrl":
            MessageLookupByLibrary.simpleMessage("Enter valid url"),
        "enterValidUsername":
            MessageLookupByLibrary.simpleMessage("Enter valid username"),
        "enterValue": MessageLookupByLibrary.simpleMessage("Enter Value"),
        "equal": MessageLookupByLibrary.simpleMessage("="),
        "error": MessageLookupByLibrary.simpleMessage("Error"),
        "exit": MessageLookupByLibrary.simpleMessage("Exit"),
        "expiryDate":
            MessageLookupByLibrary.simpleMessage("Expiry Date(ddmmyyyy)"),
        "five": MessageLookupByLibrary.simpleMessage("5"),
        "forward": MessageLookupByLibrary.simpleMessage("Forward"),
        "four": MessageLookupByLibrary.simpleMessage("4"),
        "home_title":
            MessageLookupByLibrary.simpleMessage("SAB Newlands Brewery"),
        "ignore_download_button":
            MessageLookupByLibrary.simpleMessage("Ignore Download"),
        "info": MessageLookupByLibrary.simpleMessage("Information"),
        "info_clear_all_count": MessageLookupByLibrary.simpleMessage(
            "All existing counts will be cleared. Do you wish to continue?"),
        "info_clear_for_new_count": MessageLookupByLibrary.simpleMessage(
            "There is an existing count for this area and section. Do you wish to continue the count?"),
        "info_clear_for_recount": MessageLookupByLibrary.simpleMessage(
            "Do you want to clear all count information for area and section?"),
        "info_clearing_count": MessageLookupByLibrary.simpleMessage(
            "Clearing existing counts&#8230;"),
        "info_connect_to_dock": MessageLookupByLibrary.simpleMessage(
            "Please ensure the device is docked, then click OK..."),
        "info_count_finished": MessageLookupByLibrary.simpleMessage(
            "All bins have been counted. Returning to Main Menu."),
        "info_count_finished_recount": MessageLookupByLibrary.simpleMessage(
            "All bins have been counted. Only Recount is available."),
        "info_decimal_value": MessageLookupByLibrary.simpleMessage(
            "Decimals can only exist in the Minor UOM field. Can the decimals be placed in the Minor UOM field for this result?"),
        "info_for_downloading_count": MessageLookupByLibrary.simpleMessage(
            "Do you want to download counts ?"),
        "info_for_empty_bin": MessageLookupByLibrary.simpleMessage(
            "Are you sure the bin is empty?"),
        "info_masterdata_refresh":
            MessageLookupByLibrary.simpleMessage("Refreshing master data"),
        "info_of_continue_count_for_download": MessageLookupByLibrary.simpleMessage(
            "A counting process is currently in progress for the same Area and Section. Would you like to?"),
        "info_re_count": MessageLookupByLibrary.simpleMessage(
            "This will clear all counts for this material in this area and section. Do you want to re-count?"),
        "info_select_area": MessageLookupByLibrary.simpleMessage(
            "Please make a selection for Area."),
        "info_select_brewery_area": MessageLookupByLibrary.simpleMessage(
            "Please make a selection for Brewery Area."),
        "info_select_section": MessageLookupByLibrary.simpleMessage(
            "Please make a selection for Section."),
        "info_upload_count": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to upload all count data?"),
        "info_upload_count_success": MessageLookupByLibrary.simpleMessage(
            "Stock Count data uploaded successfully"),
        "invalidDate":
            MessageLookupByLibrary.simpleMessage("Invalid date(ddmmyy)"),
        "invalidNumber":
            MessageLookupByLibrary.simpleMessage("Invalid number!!!"),
        "logOff": MessageLookupByLibrary.simpleMessage("5. Logoff"),
        "login": MessageLookupByLibrary.simpleMessage("Login"),
        "loginId": MessageLookupByLibrary.simpleMessage("Login ID"),
        "logsSentToServer": MessageLookupByLibrary.simpleMessage(
            "Logs successfully sent to Server"),
        "mainMenu": MessageLookupByLibrary.simpleMessage("Main Menu"),
        "majUom": MessageLookupByLibrary.simpleMessage("Maj Uom"),
        "minUom": MessageLookupByLibrary.simpleMessage("Min Uom"),
        "multiply": MessageLookupByLibrary.simpleMessage("X"),
        "na": MessageLookupByLibrary.simpleMessage("NA"),
        "newCount": MessageLookupByLibrary.simpleMessage("1. New Count"),
        "nine": MessageLookupByLibrary.simpleMessage("9"),
        "no": MessageLookupByLibrary.simpleMessage("NO"),
        "noResponseFromServer":
            MessageLookupByLibrary.simpleMessage("No Response from Server"),
        "no_internet_connection": MessageLookupByLibrary.simpleMessage(
            "No Internet connection. Make sure your device is connected to the network and try again."),
        "no_server_response":
            MessageLookupByLibrary.simpleMessage("No Response from Server"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "one": MessageLookupByLibrary.simpleMessage("1"),
        "options": MessageLookupByLibrary.simpleMessage("Options"),
        "password": MessageLookupByLibrary.simpleMessage("Password"),
        "pickURL": MessageLookupByLibrary.simpleMessage("Pick URL"),
        "pleaseEnterYourPasswordToLogin": MessageLookupByLibrary.simpleMessage(
            "Please Enter Your Password To Login"),
        "pleaseSelectAccountToContinue": MessageLookupByLibrary.simpleMessage(
            "Please Select Account To Continue"),
        "pleaseWaitThreeDots":
            MessageLookupByLibrary.simpleMessage("Please Wait..."),
        "please_wait": MessageLookupByLibrary.simpleMessage("Please wait"),
        "proceed_button": MessageLookupByLibrary.simpleMessage("Proceed"),
        "quality": MessageLookupByLibrary.simpleMessage("Quality"),
        "reCount": MessageLookupByLibrary.simpleMessage("Re-Count"),
        "recountSKU": MessageLookupByLibrary.simpleMessage("Recount SKU"),
        "refreshingMasterData":
            MessageLookupByLibrary.simpleMessage("Refreshing master data"),
        "reset_data":
            MessageLookupByLibrary.simpleMessage("Clear all application data"),
        "reset_warn": MessageLookupByLibrary.simpleMessage(
            "This option will clear all the data associated with the application and restore the application to the freshly installed state. Deleted data cannot be recovered. Are you sure want to clear the data in the application"),
        "reverse": MessageLookupByLibrary.simpleMessage("Reverse"),
        "sabNewLandsBrewery":
            MessageLookupByLibrary.simpleMessage("SAB Newlands Brewery"),
        "save": MessageLookupByLibrary.simpleMessage("Save"),
        "section": MessageLookupByLibrary.simpleMessage("Section"),
        "sendLogs":
            MessageLookupByLibrary.simpleMessage(">SEND LOGS TO SERVER"),
        "sendLogsToSerever":
            MessageLookupByLibrary.simpleMessage("SEND LOGS TO SERVER"),
        "setValue": MessageLookupByLibrary.simpleMessage("SET VALUE"),
        "seven": MessageLookupByLibrary.simpleMessage("7"),
        "six": MessageLookupByLibrary.simpleMessage("6"),
        "sku": MessageLookupByLibrary.simpleMessage("SKU"),
        "somethingWentWrongPleaseTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Something went wrong.please try again."),
        "splitBin": MessageLookupByLibrary.simpleMessage("Split Bin"),
        "startCount": MessageLookupByLibrary.simpleMessage("Start Count"),
        "stockTakeSystem":
            MessageLookupByLibrary.simpleMessage("STOCK TAKE SYSTEM"),
        "substraction": MessageLookupByLibrary.simpleMessage("-"),
        "three": MessageLookupByLibrary.simpleMessage("3"),
        "two": MessageLookupByLibrary.simpleMessage("2"),
        "unrestricted": MessageLookupByLibrary.simpleMessage("Unrestricted"),
        "unviredLogin": MessageLookupByLibrary.simpleMessage("Unvired Login"),
        "uploadCount": MessageLookupByLibrary.simpleMessage("3. Upload Count"),
        "uploading_count":
            MessageLookupByLibrary.simpleMessage("Uploading Count"),
        "url": MessageLookupByLibrary.simpleMessage("Url"),
        "userName": MessageLookupByLibrary.simpleMessage("Username"),
        "version": MessageLookupByLibrary.simpleMessage("Version : "),
        "warning": MessageLookupByLibrary.simpleMessage("Warning"),
        "yes": MessageLookupByLibrary.simpleMessage("YES"),
        "zero": MessageLookupByLibrary.simpleMessage("0")
      };
}

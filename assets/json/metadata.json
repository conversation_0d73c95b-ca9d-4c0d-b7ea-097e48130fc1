{"name": "NSTS", "description": "Raw Material Stock take Application", "version": "1.1", "BREWERY": {"description": "Brewery Header sctructure for Raw Mat", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "BREWERY_HEADER": {"className": "co.za.abinbev.nsts.be.BREWERY_HEADER", "header": true, "field": [{"name": "BREWERY_ID", "description": "Brewery ID", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "BREWERY_DESC", "description": "Brewery Description", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}]}, "AREA_BIN_MAP": {"className": "co.za.abinbev.nsts.be.AREA_BIN_MAP", "field": [{"name": "BREWERYAREA", "description": "Brewery Area", "isGid": true, "length": "15", "mandatory": true, "sqlType": "TEXT"}, {"name": "AREASECTION", "description": "Area Section", "isGid": true, "length": "15", "mandatory": true, "sqlType": "TEXT"}, {"name": "BIN", "description": "Bin", "isGid": true, "length": "6", "mandatory": true, "sqlType": "TEXT"}]}, "AREA_COUNTSEQ_MAP": {"className": "co.za.abinbev.nsts.be.AREA_COUNTSEQ_MAP", "field": [{"name": "BREWERYAREA", "description": "Brewery Area", "isGid": true, "length": "15", "mandatory": true, "sqlType": "TEXT"}, {"name": "AREASECTION", "description": "Area Section", "isGid": true, "length": "15", "mandatory": true, "sqlType": "TEXT"}, {"name": "COUNTSEQUENCE", "description": "Count Se<PERSON>", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_DOWNLOAD_COUNT": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": false, "INPUT_DOWNLOAD_COUNT_HEADER": {"description": "Description", "className": "co.za.abinbev.nsts.be.INPUT_DOWNLOAD_COUNT_HEADER", "header": true, "field": [{"name": "BREWERY_ID", "description": "Brewery ID char type", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "AREA", "description": "Brewery Area char type", "isGid": true, "length": "15", "mandatory": true, "sqlType": "TEXT"}]}}, "MATERIAL": {"description": "Raw Material Master Data Detail", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "MATERIAL_HEADER": {"className": "co.za.abinbev.nsts.be.MATERIAL_HEADER", "header": true, "field": [{"name": "MAT_NO", "description": "Material Number", "isGid": true, "length": "18", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "description": "Material Description (Short Text)", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "BATCH_REQ", "description": "Batch management requirement indicator", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "BARCODE", "description": "Barcode used for Raw Material Count", "isGid": false, "length": "18", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAJOR_UOM", "description": "Convert to Major UOM", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "MINOR_UOM", "description": "Convert to Minor UOM", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXPIRY_REQ", "description": "General Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "STOCK_COUNT": {"description": "Brewery Header sctructure for Raw Mat", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "STOCK_COUNT_HEADER": {"className": "co.za.abinbev.nsts.be.STOCK_COUNT_HEADER", "header": true, "field": [{"name": "BREWERY_ID", "description": "Brewery ID", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESC", "description": "Brewery Description", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}]}, "STOCK_COUNT_ITEM": {"className": "co.za.abinbev.nsts.be.STOCK_COUNT_ITEM", "field": [{"name": "MAT_NO", "description": "Material Number", "isGid": false, "length": "18", "mandatory": false, "sqlType": "TEXT"}, {"name": "BATCH_NO", "description": "Batch Number", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "DOMBBD", "description": "DOM or Best Before Date", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "AREA", "description": "Brewery Area", "isGid": false, "length": "15", "mandatory": false, "sqlType": "TEXT"}, {"name": "AREASECTION", "description": "Area Section", "isGid": false, "length": "15", "mandatory": false, "sqlType": "TEXT"}, {"name": "BIN", "description": "Bin", "isGid": false, "length": "6", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAJOR_UNIT_QTY", "description": "Quantity in Major UOM", "isGid": false, "length": "7", "mandatory": false, "sqlType": "REAL"}, {"name": "MINOR_UNIT_QTY", "description": "Quantity in Minor UOM", "isGid": false, "length": "7", "mandatory": false, "sqlType": "REAL"}, {"name": "PALLET", "description": "Material Number", "isGid": false, "length": "18", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATUS", "description": "Status Indicator", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "COUNT_DATE", "description": "Count Date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIRECTION", "description": "Count Direction", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "COUNT_ID", "description": "Count Id", "isGid": true, "length": "0", "mandatory": true, "sqlType": "INTEGER"}, {"name": "SPLIT_BIN", "description": "confirm a split bin status", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "EXPIRY_DT", "description": "Value date, format DDMMYYYY", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}]}}, "Index": []}